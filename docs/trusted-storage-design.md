# Trusty TEE 可信存储新设计方案

## 1. 概述

### 1.1 设计目标

本文档详细描述 Trusty TEE 项目中可信存储（trusted storage）的新设计方案，基于 OP-TEE 成熟的双层对象模型，在 Trusty 用户空间环境中实现 GP 标准的可信存储功能。

**核心设计目标：**
- **OP-TEE 架构适配**：完全基于 OP-TEE 的 tee_obj + tee_pobj 双层对象模型
- **用户空间实现**：所有对象管理在用户空间完成，避免内核修改
- **简化设计原则**：保持方案简洁，避免过度复杂化
- **GP 标准兼容**：上层提供完整的 GP 存储 API 接口

### 1.2 适用范围和限制

**适用范围：**
- 支持 GP 标准的瞬时对象（Transient Object）和持久化对象（Persistent Object）
- 适用于单实例 TA 环境下的存储需求
- 支持完整的 GP 存储 API 接口

**设计限制：**
- 多实例支持将在后续版本中单独设计，本文档暂不涉及
- panic 流程将在后续版本中单独设计，本文档暂不涉及
- 当前设计主要针对功能实现，性能优化将在后续迭代中完善

### 1.3 整体架构

基于 OP-TEE 设计原理，采用双层对象模型：

```mermaid
graph TB
    subgraph "GP API Layer"
        A1[TEE_OpenPersistentObject]
        A2[TEE_CreatePersistentObject]
        A3[TEE_AllocateTransientObject]
        A4[TEE_ReadObjectData]
        A5[TEE_WriteObjectData]
    end

    subgraph "TA 用户空间 - tee_obj 管理"
        B1[struct trusty_tee_obj]
        B2[libutee 库管理]
        B3[TA 对象链表]
    end

    subgraph "存储 TA - tee_pobj 管理"
        C1[struct trusty_pobj]
        C2[持久对象全局管理]
        C3[直接存储操作]
    end

    subgraph "Trusty 存储服务"
        D1[storage_open_file]
        D2[storage_read/write]
        D3[storage_delete_file]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B1
    A4 --> B1
    A5 --> B1

    B1 --> C1
    B2 --> B1
    B3 --> B1

    C1 --> D1
    C2 --> C1
    C3 --> C1

    D1 --> D2
    D2 --> D3
```

## 2. 瞬时对象/句柄管理

### 2.1 数据结构设计

基于 OP-TEE 的 tee_obj 设计，但适配 Trusty 用户空间环境。tee_obj 既是瞬时对象又是句柄，由 TA 在用户空间自主管理，无需内核维护链表。

#### 2.1.1 核心数据结构（基于 OP-TEE tee_obj，适配 Trusty）

```c
/* Trusty TEE 对象句柄 - 基于 OP-TEE tee_obj 设计，适配用户空间 */
struct trusty_tee_obj {
    /* 链表管理 - 在 libutee 库中维护 */
    struct list_node link;         /* TA 对象链表节点 */

    /* GP 标准对象信息 - 完全兼容 OP-TEE */
    TEE_ObjectInfo info;           /* GP 标准对象信息 */

    /* 并发控制 - 完全保持 OP-TEE 设计 */
    bool busy;                     /* 操作忙标志，防止并发操作 */

    /* 属性管理 - 完全保持 OP-TEE 设计 */
    uint32_t have_attrs;           /* 属性位字段，标识已设置的属性 */
    void *attr;                    /* 属性数据指针 */

    /* 数据流管理 - 完全保持 OP-TEE 设计 */
    size_t ds_pos;                 /* 数据流起始位置偏移 */

    /* 存储连接 - 适配 Trusty 机制 */
    struct trusty_pobj *pobj;      /* 指向持久化对象的指针 */
    handle_t storage_handle;       /* 存储 TA 连接句柄 */

    /* Trusty 用户空间扩展 */
    mutex_t obj_lock;              /* 对象锁 */
};

/* libutee 库中的对象管理上下文 */
struct utee_object_context {
    struct list_node objects;      /* TA 对象链表头 */
    uint32_t object_count;         /* 当前对象数量 */
    uint32_t max_objects;          /* 最大对象数量限制 */
    mutex_t objects_lock;          /* 对象链表锁 */
};

/* 全局对象管理上下文（在 libutee 库中） */
extern struct utee_object_context g_utee_obj_ctx;
```

#### 2.1.2 OP-TEE vs Trusty 对比

| OP-TEE 字段 | Trusty TEE 字段 | 差异说明 |
|-------------|-----------------|----------|
| `TAILQ_ENTRY(tee_obj) link` | `struct list_node link` | 在 libutee 库中维护链表 |
| `TEE_ObjectInfo info` | `TEE_ObjectInfo info` | 完全保持 GP 标准对象信息 |
| `bool busy` | `bool busy` | 完全保持并发控制标志 |
| `uint32_t have_attrs` | `uint32_t have_attrs` | 完全保持属性位字段 |
| `void *attr` | `void *attr` | 完全保持属性数据指针 |
| `size_t ds_pos` | `size_t ds_pos` | 完全保持数据流位置 |
| `struct tee_pobj *pobj` | `struct trusty_pobj *pobj` | 适配 Trusty 持久对象 |
| `struct tee_file_handle *fh` | `handle_t storage_handle` | 适配 Trusty IPC 句柄 |

#### 2.1.3 Trusty 对象管理模型

```mermaid
graph TB
    subgraph "TA 用户空间"
        A[libutee 库]
        A --> B[对象链表管理]
        B --> C[tee_obj 1]
        B --> D[tee_obj 2]
        B --> E[tee_obj N]
        C --> F[存储 TA 连接]
        D --> G[存储 TA 连接]
        E --> H[存储 TA 连接]
    end

    subgraph "存储 TA"
        I[tee_pobj 全局管理]
        F --> I
        G --> I
        H --> I
    end

    subgraph "TA Panic 处理"
        J[TA Panic]
        J --> K[libutee 对象链表自动清理]
        J --> L[通知存储 TA 清理 tee_pobj]
    end
```

### 2.2 管理机制

TA 通过 libutee 库管理 tee_obj，使用链表数据结构。使用对象地址作为 handle，TA panic 时对象链表自动清理。

#### 2.2.1 对象分配操作（libutee 库实现）

```c
/**
 * 分配新的 tee_obj 对象 - libutee 库实现
 * @return: 成功返回对象指针（作为 handle），失败返回 NULL
 */
struct trusty_tee_obj *utee_obj_alloc(void) {
    struct trusty_tee_obj *obj;

    mutex_acquire(&g_utee_obj_ctx.objects_lock);

    /* 检查对象数量限制 */
    if (g_utee_obj_ctx.object_count >= g_utee_obj_ctx.max_objects) {
        mutex_release(&g_utee_obj_ctx.objects_lock);
        return NULL;
    }

    /* 分配对象结构 */
    obj = calloc(1, sizeof(*obj));
    if (!obj) {
        mutex_release(&g_utee_obj_ctx.objects_lock);
        return NULL;
    }

    /* 初始化对象 - 基于 OP-TEE 初始化逻辑 */
    list_initialize(&obj->link);
    memset(&obj->info, 0, sizeof(obj->info));
    obj->busy = false;
    obj->have_attrs = 0;
    obj->attr = NULL;
    obj->ds_pos = 0;
    obj->pobj = NULL;
    obj->storage_handle = INVALID_IPC_HANDLE;
    mutex_init(&obj->obj_lock);

    /* 添加到 libutee 的对象链表 */
    list_add_tail(&g_utee_obj_ctx.objects, &obj->link);
    g_utee_obj_ctx.object_count++;

    mutex_release(&g_utee_obj_ctx.objects_lock);

    /* 返回对象地址作为 handle */
    return obj;
}

/**
 * 释放 tee_obj 对象 - libutee 库实现
 * @param obj: 要释放的对象
 */
void utee_obj_free(struct trusty_tee_obj *obj) {
    if (!obj) {
        return;
    }

    mutex_acquire(&g_utee_obj_ctx.objects_lock);
    mutex_acquire(&obj->obj_lock);

    /* 清理资源 - 基于 OP-TEE 清理逻辑 */
    if (obj->pobj) {
        /* 通知存储 TA 减少持久对象的引用计数 */
        utee_storage_pobj_put(obj->pobj);
        obj->pobj = NULL;
    }

    if (obj->storage_handle != INVALID_IPC_HANDLE) {
        close(obj->storage_handle);
        obj->storage_handle = INVALID_IPC_HANDLE;
    }

    if (obj->attr) {
        /* 安全清除属性数据 */
        memset(obj->attr, 0, obj->have_attrs * sizeof(TEE_Attribute));
        free(obj->attr);
        obj->attr = NULL;
    }

    /* 从链表中移除 */
    list_delete(&obj->link);
    g_utee_obj_ctx.object_count--;

    mutex_release(&obj->obj_lock);
    mutex_destroy(&obj->obj_lock);

    /* 清除对象结构 */
    memset(obj, 0, sizeof(*obj));
    free(obj);

    mutex_release(&g_utee_obj_ctx.objects_lock);
}
```

#### 2.2.2 对象验证操作（libutee 库实现）

```c
/**
 * 验证 handle 是否有效 - libutee 库实现
 * @param handle: 对象 handle（实际是 tee_obj 地址）
 * @return: 成功返回对象指针，失败返回 NULL
 */
struct trusty_tee_obj *utee_obj_get(TEE_ObjectHandle handle) {
    struct trusty_tee_obj *obj = (struct trusty_tee_obj *)handle;
    struct trusty_tee_obj *found_obj = NULL;

    if (!obj) {
        return NULL;
    }

    mutex_acquire(&g_utee_obj_ctx.objects_lock);

    /* 在 libutee 的对象链表中查找，验证 handle 有效性 */
    list_for_every_entry(&g_utee_obj_ctx.objects, found_obj,
                         struct trusty_tee_obj, link) {
        if (found_obj == obj) {
            /* handle 有效，返回对象 */
            mutex_release(&g_utee_obj_ctx.objects_lock);
            return obj;
        }
    }

    mutex_release(&g_utee_obj_ctx.objects_lock);
    return NULL;  /* handle 无效 */
}

/**
 * 设置对象忙状态 - 基于 OP-TEE 逻辑
 * @param obj: 对象指针
 * @return: 成功返回 TEE_SUCCESS，失败返回错误码
 */
TEE_Result utee_obj_set_busy(struct trusty_tee_obj *obj) {
    if (!obj) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    mutex_acquire(&obj->obj_lock);

    if (obj->busy) {
        mutex_release(&obj->obj_lock);
        return TEE_ERROR_BUSY;
    }

    obj->busy = true;
    mutex_release(&obj->obj_lock);
    return TEE_SUCCESS;
}

/**
 * 清除对象忙状态 - 基于 OP-TEE 逻辑
 * @param obj: 对象指针
 */
void utee_obj_clear_busy(struct trusty_tee_obj *obj) {
    if (!obj) {
        return;
    }

    mutex_acquire(&obj->obj_lock);
    obj->busy = false;
    mutex_release(&obj->obj_lock);
}
```

### 2.3 生命周期管理

在 Trusty 用户空间中，tee_obj 由 libutee 库管理，TA panic 时 libutee 对象链表会自动清理。

#### 2.3.1 TA panic 时的自动清理

```c
/*
 * 重要说明：在 Trusty 用户空间环境中，TA panic 时的处理与 OP-TEE 不同
 *
 * OP-TEE：
 * - tee_obj 在内核中，需要内核主动清理
 * - 内核维护 TA 上下文中的对象链表
 * - panic 时遍历链表清理所有对象
 *
 * Trusty：
 * - tee_obj 在 TA 用户空间的 libutee 库中
 * - TA panic 时，整个用户空间被销毁
 * - libutee 库中的对象链表自动清理
 *
 * 真正需要处理的是：清理该 TA 在存储 TA 中的持久化对象
 */

/**
 * TA panic 时的处理流程 - Trusty 版本
 * @param ta_uuid: panic 的 TA UUID
 */
void handle_ta_panic(const struct uuid *ta_uuid) {
    /*
     * 步骤 1: tee_obj 自动清理
     * - TA 用户空间被销毁时，libutee 库中的所有 tee_obj 自动释放
     * - 包括对象链表、内存、锁等资源
     * - 无需特殊处理
     */

    /*
     * 步骤 2: 通知存储 TA 清理该 TA 的持久化对象
     * - 这是唯一需要主动处理的部分
     */
    notify_storage_ta_cleanup(ta_uuid);
}

/**
 * libutee 库初始化 - 在 TA 启动时调用
 */
void utee_obj_context_init(void) {
    list_initialize(&g_utee_obj_ctx.objects);
    g_utee_obj_ctx.object_count = 0;
    g_utee_obj_ctx.max_objects = MAX_OBJECTS_PER_TA;
    mutex_init(&g_utee_obj_ctx.objects_lock);
}

/**
 * libutee 库清理 - 在 TA 退出时调用（可选）
 */
void utee_obj_context_cleanup(void) {
    struct trusty_tee_obj *obj, *temp;

    mutex_acquire(&g_utee_obj_ctx.objects_lock);

    /* 清理所有剩余对象 */
    list_for_every_entry_safe(&g_utee_obj_ctx.objects, obj, temp,
                              struct trusty_tee_obj, link) {
        utee_obj_free(obj);
    }

    mutex_release(&g_utee_obj_ctx.objects_lock);
    mutex_destroy(&g_utee_obj_ctx.objects_lock);
}
```

## 3. 持久化对象管理

### 3.1 存储 TA 架构

存储 TA 负责所有 tee_pobj 的统一管理。tee_pobj 在存储 TA 里，可以直接调用对应的存储接口，简化设计。

#### 3.1.1 存储 TA 数据结构（基于 OP-TEE tee_pobj）

```c
/* Trusty 持久对象 - 基于 OP-TEE tee_pobj 设计，优化管理结构 */
struct trusty_pobj {
    /* 对象链表管理 - 挂在 TA 实例节点下 */
    struct list_node link;        /* 持久化对象链表节点 */

    /* 引用计数 - 完全保持 OP-TEE 设计 */
    uint32_t refcnt;              /* 引用计数，支持多句柄访问同一对象 */

    /* 对象标识 - 完全保持 OP-TEE 设计，以用户指定为准 */
    void *obj_id;                 /* 对象标识符，由用户 TA 指定，不修改 */
    uint32_t obj_id_len;          /* 对象标识符长度 */

    /* 访问控制 - 完全保持 OP-TEE 设计 */
    uint32_t flags;               /* 访问标志（读/写/共享权限） */
    uint32_t obj_info_usage;      /* 对象使用权限（密钥用途等） */
    uint32_t object_type;         /* 对象类型 */
    uint32_t data_size;           /* 数据大小 */

    /* 状态管理 - 完全保持 OP-TEE 设计 */
    bool temporary;               /* 临时对象标志，创建过程中为 true */
    bool creating;                /* 创建中标志，防止并发访问冲突 */

    /* 存储路径 - 基于 TA UUID + 实例号 + 对象 ID 生成 */
    char storage_path[256];       /* 存储路径 */

    /* 对象锁 */
    mutex_t pobj_lock;            /* 持久对象锁 */
};

/* TA 实例节点 - 双重链表的第一层：按 TA UUID + 实例号管理 */
struct ta_instance_node {
    /* 实例链表管理 */
    struct list_node link;              /* 全局 TA 实例链表节点 */

    /* TA 实例标识 */
    struct uuid ta_uuid;                /* TA UUID */
    uint32_t instance_id;               /* TA 实例号 */

    /* 该实例的持久对象链表 - 双重链表的第二层 */
    struct list_node pobj_list;         /* 该实例的持久对象链表头 */
    uint32_t pobj_count;                /* 该实例的持久对象数量 */
    mutex_t pobj_list_lock;             /* 该实例的持久对象链表锁 */

    /* 实例状态 */
    bool active;                        /* 实例是否活跃 */
    uint64_t last_access_time;          /* 最后访问时间 */
};

/* 存储 TA 上下文 - 双重链表管理 */
struct storage_ta_context {
    /* 全局 TA 实例管理 - 双重链表的第一层 */
    struct list_node ta_instance_list;  /* 全局 TA 实例链表头 */
    mutex_t ta_instance_list_lock;      /* 全局 TA 实例链表锁 */
    uint32_t ta_instance_count;         /* TA 实例总数 */

    /* 全局统计信息 */
    uint32_t total_pobj_count;          /* 全局持久对象总数 */
    uint64_t total_operations;          /* 总操作数 */
    uint64_t failed_operations;         /* 失败操作数 */

    /* 存储服务连接 - 全局共享 */
    handle_t storage_service_handle;    /* Trusty 存储服务连接句柄 */
};
```

#### 3.1.2 双重链表管理架构

```mermaid
graph TB
    subgraph "存储 TA - 双重链表管理"
        A[storage_ta_context]
        A --> B[ta_instance_list 全局链表]

        B --> C[TA实例1<br/>UUID + instance_id]
        B --> D[TA实例2<br/>UUID + instance_id]
        B --> E[TA实例N<br/>UUID + instance_id]

        C --> F[pobj_list 对象链表]
        D --> G[pobj_list 对象链表]
        E --> H[pobj_list 对象链表]

        F --> I[持久对象1]
        F --> J[持久对象2]
        G --> K[持久对象3]
        G --> L[持久对象4]
        H --> M[持久对象5]
    end

    subgraph "Panic 清理机制"
        N[检测到 TA Panic]
        N --> O[根据 UUID + instance_id<br/>找到对应 TA 实例节点]
        O --> P[遍历该实例的 pobj_list]
        P --> Q[删除所有持久对象]
        Q --> R[删除 TA 实例节点]
    end
```

#### 3.1.3 OP-TEE 字段映射表

| OP-TEE 字段 | Trusty TEE 字段 | 映射说明 |
|-------------|-----------------|----------|
| `TAILQ_ENTRY(tee_pobj) link` | `struct list_node link` | 挂在 TA 实例节点的对象链表下 |
| `uint32_t refcnt` | `uint32_t refcnt` | 完全保持引用计数机制 |
| `struct tee_ta_session *ta_session` | `通过 ta_instance_node 管理` | 通过双重链表结构管理 TA 隔离 |
| `void *obj_id` | `void *obj_id` | 完全保持对象标识符，以用户指定为准 |
| `uint32_t obj_id_len` | `uint32_t obj_id_len` | 完全保持标识符长度 |
| `uint32_t flags` | `uint32_t flags` | 完全保持访问标志 |
| `uint32_t obj_info_usage` | `uint32_t obj_info_usage` | 完全保持使用权限 |
| `bool temporary` | `bool temporary` | 完全保持临时对象标志 |
| `bool creating` | `bool creating` | 完全保持创建中标志 |
| `const struct tee_file_operations *fops` | `无需 fops 字段` | 直接在存储 TA 中调用 Trusty 存储接口 |

### 3.2 双重链表管控职责

存储 TA 采用双重链表结构管理所有 tee_pobj，实现高效的 TA 实例隔离和 panic 清理。

#### 3.2.1 双重链表管控功能

**第一层：TA 实例管理**
- 基于 TA UUID + 实例号创建 TA 实例节点
- 每个 TA 实例节点管理该实例的所有持久对象
- 支持多实例 TA 的完全隔离
- TA panic 时可以精确清理对应实例的所有对象

**第二层：持久对象管理**
- 每个 TA 实例节点下维护独立的持久对象链表
- 基于用户指定的对象 ID 查找和创建对象
- 引用计数管理，支持多个 tee_obj 引用同一个 tee_pobj
- 对象 ID 完全以用户指定为准，不做任何修改

**并发控制策略：**
- 全局 TA 实例链表锁保护实例节点操作
- 每个实例独立的对象链表锁保护对象操作
- creating 标志防止创建过程中的并发访问
- 引用计数保护，管理多句柄访问

#### 3.2.2 双重链表管控操作实现

```c
/**
 * 查找或创建 TA 实例节点
 * @param storage_ctx: 存储 TA 上下文
 * @param ta_uuid: TA UUID
 * @param instance_id: TA 实例号
 * @return: 成功返回 TA 实例节点指针，失败返回 NULL
 */
struct ta_instance_node *find_or_create_ta_instance(struct storage_ta_context *storage_ctx,
                                                   const struct uuid *ta_uuid,
                                                   uint32_t instance_id) {
    struct ta_instance_node *ta_node;

    if (!storage_ctx || !ta_uuid) {
        return NULL;
    }

    mutex_acquire(&storage_ctx->ta_instance_list_lock);

    /* 查找现有 TA 实例节点 */
    list_for_every_entry(&storage_ctx->ta_instance_list, ta_node,
                         struct ta_instance_node, link) {
        if (memcmp(&ta_node->ta_uuid, ta_uuid, sizeof(*ta_uuid)) == 0 &&
            ta_node->instance_id == instance_id) {
            /* 找到实例节点，更新访问时间 */
            ta_node->last_access_time = current_time_ns();
            ta_node->active = true;
            mutex_release(&storage_ctx->ta_instance_list_lock);
            return ta_node;
        }
    }

    /* 创建新的 TA 实例节点 */
    ta_node = calloc(1, sizeof(*ta_node));
    if (!ta_node) {
        mutex_release(&storage_ctx->ta_instance_list_lock);
        return NULL;
    }

    /* 初始化 TA 实例节点 */
    list_initialize(&ta_node->link);
    memcpy(&ta_node->ta_uuid, ta_uuid, sizeof(*ta_uuid));
    ta_node->instance_id = instance_id;
    list_initialize(&ta_node->pobj_list);
    ta_node->pobj_count = 0;
    mutex_init(&ta_node->pobj_list_lock);
    ta_node->active = true;
    ta_node->last_access_time = current_time_ns();

    /* 添加到全局 TA 实例链表 */
    list_add_tail(&storage_ctx->ta_instance_list, &ta_node->link);
    storage_ctx->ta_instance_count++;

    mutex_release(&storage_ctx->ta_instance_list_lock);
    return ta_node;
}

/**
 * 查找或创建持久对象 - 双重链表版本
 * @param storage_ctx: 存储 TA 上下文
 * @param ta_uuid: TA UUID
 * @param instance_id: TA 实例号
 * @param obj_id: 对象标识符（用户指定，不修改）
 * @param obj_id_len: 对象标识符长度
 * @return: 成功返回 tee_pobj 指针，失败返回 NULL
 */
struct trusty_pobj *trusty_pobj_get(struct storage_ta_context *storage_ctx,
                                   const struct uuid *ta_uuid,
                                   uint32_t instance_id,
                                   const void *obj_id,
                                   uint32_t obj_id_len) {
    struct ta_instance_node *ta_node;
    struct trusty_pobj *pobj;

    if (!storage_ctx || !ta_uuid || !obj_id || obj_id_len == 0) {
        return NULL;
    }

    /* 查找或创建 TA 实例节点 */
    ta_node = find_or_create_ta_instance(storage_ctx, ta_uuid, instance_id);
    if (!ta_node) {
        return NULL;
    }

    mutex_acquire(&ta_node->pobj_list_lock);

    /* 在该 TA 实例的对象链表中查找现有对象 */
    list_for_every_entry(&ta_node->pobj_list, pobj, struct trusty_pobj, link) {
        if (pobj->obj_id_len == obj_id_len &&
            memcmp(pobj->obj_id, obj_id, obj_id_len) == 0) {
            /* 找到对象，增加引用计数 */
            pobj->refcnt++;
            mutex_release(&ta_node->pobj_list_lock);
            return pobj;
        }
    }

    /* 创建新的持久对象 */
    pobj = calloc(1, sizeof(*pobj));
    if (!pobj) {
        mutex_release(&ta_node->pobj_list_lock);
        return NULL;
    }

    /* 初始化持久对象 */
    list_initialize(&pobj->link);
    pobj->refcnt = 1;

    /* 复制用户指定的对象 ID，不做任何修改 */
    pobj->obj_id = malloc(obj_id_len);
    if (!pobj->obj_id) {
        free(pobj);
        mutex_release(&ta_node->pobj_list_lock);
        return NULL;
    }
    memcpy(pobj->obj_id, obj_id, obj_id_len);
    pobj->obj_id_len = obj_id_len;

    pobj->flags = 0;
    pobj->obj_info_usage = 0;
    pobj->object_type = 0;
    pobj->data_size = 0;
    pobj->temporary = false;
    pobj->creating = false;

    /* 生成存储路径：基于 TA UUID + 实例号 + 用户对象 ID */
    generate_storage_path_with_instance(pobj->storage_path, sizeof(pobj->storage_path),
                                       ta_uuid, instance_id, obj_id, obj_id_len);

    mutex_init(&pobj->pobj_lock);

    /* 添加到该 TA 实例的对象链表 */
    list_add_tail(&ta_node->pobj_list, &pobj->link);
    ta_node->pobj_count++;
    storage_ctx->total_pobj_count++;

    mutex_release(&ta_node->pobj_list_lock);
    return pobj;
}

/**
 * 释放持久对象引用 - 双重链表版本
 * @param ta_node: TA 实例节点
 * @param pobj: 持久对象指针
 */
void trusty_pobj_put(struct ta_instance_node *ta_node, struct trusty_pobj *pobj) {
    if (!ta_node || !pobj) {
        return;
    }

    mutex_acquire(&ta_node->pobj_list_lock);

    if (--pobj->refcnt == 0) {
        /* 引用计数为 0，从该实例的对象链表中移除并释放 */
        list_delete(&pobj->link);
        ta_node->pobj_count--;
        mutex_release(&ta_node->pobj_list_lock);

        /* 清理资源 */
        if (pobj->obj_id) {
            free(pobj->obj_id);
        }
        mutex_destroy(&pobj->pobj_lock);
        free(pobj);
    } else {
        mutex_release(&ta_node->pobj_list_lock);
    }
}

/**
 * 生成包含实例号的存储路径
 * @param path: 输出路径缓冲区
 * @param path_size: 路径缓冲区大小
 * @param ta_uuid: TA UUID
 * @param instance_id: TA 实例号
 * @param obj_id: 用户指定的对象 ID
 * @param obj_id_len: 对象 ID 长度
 * @return: 成功返回 0，失败返回负数错误码
 */
int generate_storage_path_with_instance(char *path, size_t path_size,
                                       const struct uuid *ta_uuid,
                                       uint32_t instance_id,
                                       const void *obj_id, uint32_t obj_id_len) {
    char uuid_str[37];
    char obj_id_hex[TEE_OBJECT_ID_MAX_LEN * 2 + 1];

    if (!path || !ta_uuid || !obj_id || obj_id_len == 0) {
        return -EINVAL;
    }

    /* 转换 UUID 为字符串 */
    snprintf(uuid_str, sizeof(uuid_str),
             "%08x-%04x-%04x-%04x-%012llx",
             ta_uuid->timeLow, ta_uuid->timeMid, ta_uuid->timeHiAndVersion,
             ta_uuid->clockSeqAndNode[0] << 8 | ta_uuid->clockSeqAndNode[1],
             *(uint64_t *)&ta_uuid->clockSeqAndNode[2]);

    /* 转换用户对象 ID 为十六进制字符串（保持原始内容不变） */
    for (uint32_t i = 0; i < obj_id_len; i++) {
        snprintf(&obj_id_hex[i * 2], 3, "%02x", ((uint8_t *)obj_id)[i]);
    }

    /* 生成存储路径：ta_uuid/instance_id/user_obj_id */
    snprintf(path, path_size, "%s/%u/%s", uuid_str, instance_id, obj_id_hex);

    return 0;
}
```

### 3.3 存储操作设计

存储 TA 中的 tee_pobj 可以直接调用 Trusty 存储接口，无需通过函数指针抽象层。

#### 3.3.1 直接存储操作（简化设计）

```c
/**
 * 存储 TA 中的直接存储操作 - 无需 fops 抽象层
 * tee_pobj 直接调用 Trusty 存储接口
 */

/**
 * 打开持久对象文件
 * @param pobj: 持久对象
 * @param size: 输出文件大小
 * @param fh: 输出文件句柄
 * @return: TEE_Result
 */
TEE_Result trusty_pobj_open(struct trusty_pobj *pobj, size_t *size,
                           storage_session_t *fh) {
    int ret;

    /* 直接调用 Trusty 存储接口 */
    ret = storage_open_file(pobj->session, fh, pobj->storage_path,
                           STORAGE_FILE_OPEN_TRUNCATE, 0);
    if (ret < 0) {
        return TEE_ERROR_STORAGE_NOT_AVAILABLE;
    }

    /* 获取文件大小 */
    if (size) {
        ret = storage_get_file_size(*fh, size);
        if (ret < 0) {
            storage_close_file(*fh);
            return TEE_ERROR_STORAGE_NOT_AVAILABLE;
        }
    }

    return TEE_SUCCESS;
}

/**
 * 创建持久对象文件
 * @param pobj: 持久对象
 * @param initial_data: 初始数据
 * @param initial_data_len: 初始数据长度
 * @param fh: 输出文件句柄
 * @return: TEE_Result
 */
TEE_Result trusty_pobj_create(struct trusty_pobj *pobj,
                             const void *initial_data, size_t initial_data_len,
                             storage_session_t *fh) {
    int ret;

    /* 直接调用 Trusty 存储接口创建文件 */
    ret = storage_open_file(pobj->session, fh, pobj->storage_path,
                           STORAGE_FILE_OPEN_CREATE | STORAGE_FILE_OPEN_TRUNCATE,
                           STORAGE_OP_COMPLETE);
    if (ret < 0) {
        return TEE_ERROR_STORAGE_NOT_AVAILABLE;
    }

    /* 写入初始数据 */
    if (initial_data && initial_data_len > 0) {
        ret = storage_write(*fh, 0, initial_data, initial_data_len,
                           STORAGE_OP_COMPLETE);
        if (ret < 0) {
            storage_close_file(*fh);
            storage_delete_file(pobj->session, pobj->storage_path,
                               STORAGE_OP_COMPLETE);
            return TEE_ERROR_STORAGE_NOT_AVAILABLE;
        }
    }

    return TEE_SUCCESS;
}

/**
 * 读取持久对象数据
 * @param fh: 文件句柄
 * @param pos: 读取位置
 * @param buf: 数据缓冲区
 * @param len: 读取长度
 * @return: TEE_Result
 */
TEE_Result trusty_pobj_read(storage_session_t fh, size_t pos,
                           void *buf, size_t *len) {
    int ret;

    /* 直接调用 Trusty 存储接口 */
    ret = storage_read(fh, pos, buf, *len);
    if (ret < 0) {
        return TEE_ERROR_STORAGE_NOT_AVAILABLE;
    }

    *len = ret;
    return TEE_SUCCESS;
}

/**
 * 写入持久对象数据
 * @param fh: 文件句柄
 * @param pos: 写入位置
 * @param buf: 数据缓冲区
 * @param len: 写入长度
 * @return: TEE_Result
 */
TEE_Result trusty_pobj_write(storage_session_t fh, size_t pos,
                            const void *buf, size_t len) {
    int ret;

    /* 直接调用 Trusty 存储接口 */
    ret = storage_write(fh, pos, buf, len, STORAGE_OP_COMPLETE);
    if (ret < 0) {
        return TEE_ERROR_STORAGE_NOT_AVAILABLE;
    }

    return TEE_SUCCESS;
}

/**
 * 删除持久对象文件
 * @param pobj: 持久对象
 * @return: TEE_Result
 */
TEE_Result trusty_pobj_remove(struct trusty_pobj *pobj) {
    int ret;

    /* 直接调用 Trusty 存储接口 */
    ret = storage_delete_file(pobj->session, pobj->storage_path,
                             STORAGE_OP_COMPLETE);
    if (ret < 0) {
        return TEE_ERROR_STORAGE_NOT_AVAILABLE;
    }

    return TEE_SUCCESS;
}
```

#### 3.3.2 Trusty 存储接口映射

| OP-TEE 操作 | Trusty 存储接口 | 实现说明 |
|-------------|-----------------|----------|
| `open()` | `storage_open_file()` | 直接打开文件 |
| `create()` | `storage_open_file()` + CREATE 标志 | 创建新文件 |
| `close()` | `storage_close_file()` | 关闭文件句柄 |
| `read()` | `storage_read()` | 读取文件数据 |
| `write()` | `storage_write()` | 写入文件数据 |
| `truncate()` | `storage_set_file_size()` | 设置文件大小 |
| `remove()` | `storage_delete_file()` | 删除文件 |
| `rename()` | `storage_move_file()` | 重命名文件 |

## 4. 对象标识机制

### 4.1 标识格式

基于 OP-TEE 的对象标识机制，**完全以用户指定的对象 ID 为准，系统不做任何修改**。

#### 4.1.1 标识格式规范（用户主导）

**用户主导的对象 ID 设计：**
- 对象 ID 完全由用户 TA 自行定义，可以是任意二进制数据
- 对象 ID 长度由用户 TA 指定，最大长度由实现定义
- **系统不对用户对象 ID 做任何修改、转换或重新编码**
- 系统通过 TA UUID + 实例号 + 用户对象 ID 的组合确保唯一性

**用户对象 ID 示例：**
```
/* 字符串格式 */
"my_key_001"
"user_data_12345"
"config_settings"

/* 二进制格式 */
{0x01, 0x02, 0x03, 0x04}
{0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x90}

/* UUID 格式 */
"550e8400-e29b-41d4-a716-************"

/* 混合格式 */
{0x00, 0x01, "key", 0xFF}
```

**重要原则：**
- **原样保存**：用户传入什么对象 ID，系统就原样保存什么
- **原样比较**：对象查找时按字节精确比较用户对象 ID
- **原样传递**：枚举时原样返回用户对象 ID，不做任何转换

#### 4.1.2 标识符验证（基于 OP-TEE）

```c
/**
 * 验证对象 ID - 基于 OP-TEE 逻辑
 * @param obj_id: 对象标识符
 * @param obj_id_len: 对象标识符长度
 * @return: 有效返回 true，无效返回 false
 */
bool validate_object_id(const void *obj_id, uint32_t obj_id_len) {
    /* OP-TEE 允许任意对象 ID，只需要基本验证 */
    if (!obj_id || obj_id_len == 0) {
        return false;
    }

    /* 检查长度限制 */
    if (obj_id_len > TEE_OBJECT_ID_MAX_LEN) {
        return false;
    }

    /* OP-TEE 不对对象 ID 内容做特殊要求 */
    return true;
}

/**
 * 生成存储路径 - 基于 TA UUID 和对象 ID
 * @param path: 输出路径缓冲区
 * @param path_size: 路径缓冲区大小
 * @param ta_uuid: TA UUID
 * @param obj_id: 对象 ID
 * @param obj_id_len: 对象 ID 长度
 * @return: 成功返回 0，失败返回负数错误码
 */
int generate_storage_path(char *path, size_t path_size,
                         const struct uuid *ta_uuid,
                         const void *obj_id, uint32_t obj_id_len) {
    char uuid_str[37];
    char obj_id_hex[TEE_OBJECT_ID_MAX_LEN * 2 + 1];

    if (!path || !ta_uuid || !obj_id || obj_id_len == 0) {
        return -EINVAL;
    }

    /* 转换 UUID 为字符串 */
    snprintf(uuid_str, sizeof(uuid_str),
             "%08x-%04x-%04x-%04x-%012llx",
             ta_uuid->timeLow, ta_uuid->timeMid, ta_uuid->timeHiAndVersion,
             ta_uuid->clockSeqAndNode[0] << 8 | ta_uuid->clockSeqAndNode[1],
             *(uint64_t *)&ta_uuid->clockSeqAndNode[2]);

    /* 转换对象 ID 为十六进制字符串 */
    for (uint32_t i = 0; i < obj_id_len; i++) {
        snprintf(&obj_id_hex[i * 2], 3, "%02x", ((uint8_t *)obj_id)[i]);
    }

    /* 生成存储路径：ta_uuid/obj_id_hex */
    snprintf(path, path_size, "%s/%s", uuid_str, obj_id_hex);

    return 0;
}
```

### 4.2 存储路径生成规则

基于双重链表设计，存储路径生成规则为：**TA UUID + 实例号 + 用户对象 ID**，确保多实例环境下的完全隔离。

#### 4.2.1 存储路径格式

**路径格式：**
```
{ta_uuid}/{instance_id}/{user_obj_id_hex}
```

**示例：**
```
/* TA UUID: 550e8400-e29b-41d4-a716-************ */
/* 实例号: 1 */
/* 用户对象 ID: "my_key" */
存储路径: 550e8400-e29b-41d4-a716-************/1/6d795f6b6579

/* TA UUID: 550e8400-e29b-41d4-a716-************ */
/* 实例号: 2 */
/* 用户对象 ID: "my_key" */
存储路径: 550e8400-e29b-41d4-a716-************/2/6d795f6b6579
```

**关键特性：**
- **实例隔离**：不同实例的相同对象 ID 存储在不同路径
- **用户 ID 保真**：对象 ID 部分仅做十六进制编码，不改变内容
- **TA 隔离**：不同 TA 的对象完全隔离
- **路径唯一性**：三元组 (TA UUID, 实例号, 用户对象 ID) 确保路径唯一

#### 4.2.1 TA 对象 ID 管理建议

```c
/**
 * TA 对象 ID 生成建议 - 基于 OP-TEE 最佳实践
 */

/* 推荐的对象 ID 生成策略 */
enum object_id_strategy {
    OBJ_ID_STRATEGY_SEQUENTIAL,     /* 序列号策略：key_001, key_002 */
    OBJ_ID_STRATEGY_UUID,           /* UUID 策略：使用 UUID 作为对象 ID */
    OBJ_ID_STRATEGY_HASH,           /* 哈希策略：基于内容哈希生成 */
    OBJ_ID_STRATEGY_CUSTOM          /* 自定义策略：TA 自行定义 */
};

/**
 * 生成序列号对象 ID
 * @param prefix: 前缀字符串
 * @param sequence: 序列号
 * @param obj_id: 输出的对象 ID
 * @param obj_id_len: 对象 ID 缓冲区大小
 * @return: 实际对象 ID 长度
 */
uint32_t generate_sequential_object_id(const char *prefix, uint32_t sequence,
                                      void *obj_id, uint32_t obj_id_len) {
    char temp_id[64];
    uint32_t len;

    if (!prefix || !obj_id) {
        return 0;
    }

    /* 生成序列号对象 ID */
    len = snprintf(temp_id, sizeof(temp_id), "%s_%06u", prefix, sequence);

    if (len >= obj_id_len) {
        return 0;  /* 缓冲区太小 */
    }

    memcpy(obj_id, temp_id, len);
    return len;
}

/**
 * 生成 UUID 对象 ID
 * @param obj_id: 输出的对象 ID
 * @param obj_id_len: 对象 ID 缓冲区大小
 * @return: 实际对象 ID 长度
 */
uint32_t generate_uuid_object_id(void *obj_id, uint32_t obj_id_len) {
    struct uuid uuid;

    if (!obj_id || obj_id_len < sizeof(uuid)) {
        return 0;
    }

    /* 生成随机 UUID */
    if (generate_random_uuid(&uuid) != 0) {
        return 0;
    }

    memcpy(obj_id, &uuid, sizeof(uuid));
    return sizeof(uuid);
}
```

### 4.3 冲突处理

基于 OP-TEE 的设计，通过 TA UUID + 对象 ID 的组合自然避免冲突，不同 TA 可以使用相同的对象 ID 而不会冲突。

#### 4.3.1 TA 隔离机制（基于 OP-TEE）

```c
/**
 * TA 隔离验证 - 基于 OP-TEE 逻辑
 * @param pobj: 持久对象
 * @param requesting_ta_uuid: 请求访问的 TA UUID
 * @return: 有权限返回 true，无权限返回 false
 */
bool validate_ta_access(struct trusty_pobj *pobj, const struct uuid *requesting_ta_uuid) {
    if (!pobj || !requesting_ta_uuid) {
        return false;
    }

    /* OP-TEE 的 TA 隔离：只有拥有者 TA 可以访问对象 */
    return memcmp(&pobj->uuid, requesting_ta_uuid, sizeof(struct uuid)) == 0;
}

/**
 * 对象唯一性检查 - 基于 OP-TEE 逻辑
 * @param storage_ctx: 存储 TA 上下文
 * @param ta_uuid: TA UUID
 * @param obj_id: 对象 ID
 * @param obj_id_len: 对象 ID 长度
 * @return: 对象已存在返回 true，不存在返回 false
 */
bool object_exists(struct storage_ta_context *storage_ctx,
                  const struct uuid *ta_uuid,
                  const void *obj_id, uint32_t obj_id_len) {
    struct trusty_pobj *pobj;

    if (!storage_ctx || !ta_uuid || !obj_id || obj_id_len == 0) {
        return false;
    }

    mutex_acquire(&storage_ctx->pobj_list_lock);

    /* 在全局链表中查找 */
    list_for_every_entry(&storage_ctx->pobj_list, pobj, struct trusty_pobj, link) {
        if (memcmp(&pobj->uuid, ta_uuid, sizeof(*ta_uuid)) == 0 &&
            pobj->obj_id_len == obj_id_len &&
            memcmp(pobj->obj_id, obj_id, obj_id_len) == 0) {
            mutex_release(&storage_ctx->pobj_list_lock);
            return true;
        }
    }

    mutex_release(&storage_ctx->pobj_list_lock);
    return false;
}
```

## 5. 资源清理机制

### 5.1 触发条件

在 Trusty 环境中，TA panic 时的资源清理与 OP-TEE 不同：
- **tee_obj**：在 TA 用户空间，panic 时自动清理
- **tee_pobj**：在存储 TA 中，需要主动清理

#### 5.1.1 TA panic 检测（Trusty 版本）

```c
/**
 * TA panic 通知处理 - Trusty 版本
 * @param ta_uuid: 崩溃的 TA UUID
 * @return: 成功返回 0，失败返回负数错误码
 */
int handle_ta_panic_notification(const struct uuid *ta_uuid) {
    if (!ta_uuid) {
        return -EINVAL;
    }

    /*
     * 重要区别：
     * - OP-TEE：需要清理内核中的 tee_obj
     * - Trusty：tee_obj 在用户空间，自动清理
     *
     * 只需要处理：通知存储 TA 清理该 TA 的所有 tee_pobj
     */
    notify_storage_ta_cleanup(ta_uuid);

    return 0;
}
```

### 5.2 通知流程

在 Trusty 环境中，TA panic 时只需要通知存储 TA 清理该 TA 的持久对象。

#### 5.2.1 通知流程架构（Trusty 版本）

```mermaid
sequenceDiagram
    participant Kernel as Trusty 内核
    participant TA as 普通 TA (panic)
    participant StorageTA as 存储 TA

    Kernel->>Kernel: 检测到 TA panic
    Note over TA: TA 用户空间被销毁<br/>tee_obj 自动清理
    Kernel->>StorageTA: 发送清理通知<br/>(只需清理 tee_pobj)
    StorageTA->>StorageTA: 清理该 TA 的 tee_pobj
    StorageTA->>Kernel: 返回清理结果
```

#### 5.2.2 通知消息格式（简化版）

```c
/* TA 清理通知消息 - 支持实例号的双重链表设计 */
struct ta_cleanup_notification {
    struct uuid ta_uuid;                    /* 需要清理的 TA UUID */
    uint32_t instance_id;                   /* TA 实例号 */
    uint32_t cleanup_flags;                 /* 清理标志 */
    uint64_t timestamp;                     /* 时间戳 */
};

/* 清理标志定义 */
#define CLEANUP_FLAG_PANIC          (1 << 0)  /* TA panic 清理 */
#define CLEANUP_FLAG_NORMAL_EXIT    (1 << 1)  /* 正常退出清理 */
#define CLEANUP_FLAG_FORCE          (1 << 2)  /* 强制清理 */
#define CLEANUP_FLAG_ALL_INSTANCES  (1 << 3)  /* 清理该 TA 的所有实例 */

/**
 * 通知存储 TA 清理指定实例 - 双重链表版本
 * @param ta_uuid: 需要清理的 TA UUID
 * @param instance_id: 需要清理的 TA 实例号
 * @return: 成功返回 0，失败返回负数错误码
 */
int notify_storage_ta_cleanup_instance(const struct uuid *ta_uuid, uint32_t instance_id) {
    struct ta_cleanup_notification notification;
    handle_t storage_ta_handle;
    ipc_msg_t msg;
    int ret;

    if (!ta_uuid) {
        return -EINVAL;
    }

    /* 准备通知消息 */
    memcpy(&notification.ta_uuid, ta_uuid, sizeof(*ta_uuid));
    notification.instance_id = instance_id;
    notification.cleanup_flags = CLEANUP_FLAG_PANIC;
    notification.timestamp = current_time_ns();

    /* 连接存储 TA */
    ret = connect(STORAGE_TA_PORT, IPC_CONNECT_WAIT_FOR_PORT);
    if (ret < 0) {
        return ret;
    }
    storage_ta_handle = (handle_t)ret;

    /* 发送通知 */
    msg.num_iov = 1;
    msg.iov[0].iov_base = &notification;
    msg.iov[0].iov_len = sizeof(notification);
    msg.num_handles = 0;

    ret = send_msg(storage_ta_handle, &msg);
    close(storage_ta_handle);

    return ret;
}

/**
 * 通知存储 TA 清理所有实例 - 用于不知道具体实例号的情况
 * @param ta_uuid: 需要清理的 TA UUID
 * @return: 成功返回 0，失败返回负数错误码
 */
int notify_storage_ta_cleanup_all_instances(const struct uuid *ta_uuid) {
    struct ta_cleanup_notification notification;
    handle_t storage_ta_handle;
    ipc_msg_t msg;
    int ret;

    if (!ta_uuid) {
        return -EINVAL;
    }

    /* 准备通知消息 */
    memcpy(&notification.ta_uuid, ta_uuid, sizeof(*ta_uuid));
    notification.instance_id = 0;  /* 忽略实例号 */
    notification.cleanup_flags = CLEANUP_FLAG_PANIC | CLEANUP_FLAG_ALL_INSTANCES;
    notification.timestamp = current_time_ns();

    /* 连接存储 TA */
    ret = connect(STORAGE_TA_PORT, IPC_CONNECT_WAIT_FOR_PORT);
    if (ret < 0) {
        return ret;
    }
    storage_ta_handle = (handle_t)ret;

    /* 发送通知 */
    msg.num_iov = 1;
    msg.iov[0].iov_base = &notification;
    msg.iov[0].iov_len = sizeof(notification);
    msg.num_handles = 0;

    ret = send_msg(storage_ta_handle, &msg);
    close(storage_ta_handle);

    return ret;
}
```

### 5.3 清理策略

存储 TA 接收到清理通知后，只需要清理该 TA 的所有 tee_pobj。由于 TA 已经 panic，可以强制清理所有相关的持久对象。

#### 5.3.1 清理策略实现（Trusty 版本）

```c
/**
 * 清理指定 TA 实例的所有持久对象 - 双重链表版本
 * @param storage_ctx: 存储 TA 上下文
 * @param ta_uuid: 需要清理的 TA UUID
 * @param instance_id: 需要清理的 TA 实例号
 * @return: 成功返回 0，失败返回负数错误码
 */
int cleanup_ta_instance_persistent_objects(struct storage_ta_context *storage_ctx,
                                          const struct uuid *ta_uuid,
                                          uint32_t instance_id) {
    struct ta_instance_node *ta_node, *ta_temp;
    struct trusty_pobj *pobj, *pobj_temp;
    uint32_t cleaned_count = 0;
    uint32_t failed_count = 0;
    int ret;

    if (!storage_ctx || !ta_uuid) {
        return -EINVAL;
    }

    mutex_acquire(&storage_ctx->ta_instance_list_lock);

    /* 查找对应的 TA 实例节点 */
    list_for_every_entry_safe(&storage_ctx->ta_instance_list, ta_node, ta_temp,
                              struct ta_instance_node, link) {
        if (memcmp(&ta_node->ta_uuid, ta_uuid, sizeof(*ta_uuid)) == 0 &&
            ta_node->instance_id == instance_id) {

            /* 找到对应的 TA 实例，清理其所有持久对象 */
            mutex_acquire(&ta_node->pobj_list_lock);

            /* 遍历该实例的所有持久对象 */
            list_for_every_entry_safe(&ta_node->pobj_list, pobj, pobj_temp,
                                      struct trusty_pobj, link) {
                /* 强制清理持久对象 */
                ret = force_cleanup_pobj_in_instance(ta_node, pobj);
                if (ret == 0) {
                    cleaned_count++;
                } else {
                    failed_count++;
                }
            }

            mutex_release(&ta_node->pobj_list_lock);

            /* 清理完所有对象后，删除 TA 实例节点 */
            list_delete(&ta_node->link);
            storage_ctx->ta_instance_count--;
            mutex_destroy(&ta_node->pobj_list_lock);
            free(ta_node);

            break;  /* 找到并处理了目标实例，退出循环 */
        }
    }

    mutex_release(&storage_ctx->ta_instance_list_lock);

    /* 更新统计信息 */
    storage_ctx->total_operations += cleaned_count + failed_count;
    if (failed_count > 0) {
        storage_ctx->failed_operations += failed_count;
    }

    return (failed_count > 0) ? -EPARTIAL : 0;
}

/**
 * 清理指定 TA 的所有实例 - 用于不知道具体实例号的情况
 * @param storage_ctx: 存储 TA 上下文
 * @param ta_uuid: 需要清理的 TA UUID
 * @return: 成功返回 0，失败返回负数错误码
 */
int cleanup_ta_all_instances(struct storage_ta_context *storage_ctx,
                            const struct uuid *ta_uuid) {
    struct ta_instance_node *ta_node, *ta_temp;
    struct trusty_pobj *pobj, *pobj_temp;
    uint32_t cleaned_instances = 0;
    uint32_t cleaned_objects = 0;
    uint32_t failed_count = 0;
    int ret;

    if (!storage_ctx || !ta_uuid) {
        return -EINVAL;
    }

    mutex_acquire(&storage_ctx->ta_instance_list_lock);

    /* 查找该 TA 的所有实例节点 */
    list_for_every_entry_safe(&storage_ctx->ta_instance_list, ta_node, ta_temp,
                              struct ta_instance_node, link) {
        if (memcmp(&ta_node->ta_uuid, ta_uuid, sizeof(*ta_uuid)) == 0) {

            /* 找到该 TA 的一个实例，清理其所有持久对象 */
            mutex_acquire(&ta_node->pobj_list_lock);

            /* 遍历该实例的所有持久对象 */
            list_for_every_entry_safe(&ta_node->pobj_list, pobj, pobj_temp,
                                      struct trusty_pobj, link) {
                /* 强制清理持久对象 */
                ret = force_cleanup_pobj_in_instance(ta_node, pobj);
                if (ret == 0) {
                    cleaned_objects++;
                } else {
                    failed_count++;
                }
            }

            mutex_release(&ta_node->pobj_list_lock);

            /* 清理完所有对象后，删除 TA 实例节点 */
            list_delete(&ta_node->link);
            storage_ctx->ta_instance_count--;
            mutex_destroy(&ta_node->pobj_list_lock);
            free(ta_node);
            cleaned_instances++;
        }
    }

    mutex_release(&storage_ctx->ta_instance_list_lock);

    /* 更新统计信息 */
    storage_ctx->total_operations += cleaned_objects + failed_count;
    if (failed_count > 0) {
        storage_ctx->failed_operations += failed_count;
    }

    return (failed_count > 0) ? -EPARTIAL : 0;
}

/**
 * 强制清理单个持久对象 - 双重链表版本
 * @param ta_node: TA 实例节点
 * @param pobj: 要清理的持久对象
 * @return: 成功返回 0，失败返回负数错误码
 */
int force_cleanup_pobj_in_instance(struct ta_instance_node *ta_node,
                                  struct trusty_pobj *pobj) {
    int ret = 0;

    if (!ta_node || !pobj) {
        return -EINVAL;
    }

    mutex_acquire(&pobj->pobj_lock);

    /*
     * 重要：由于 TA 已经 panic，所有指向这个 tee_pobj 的 tee_obj
     * 都已经被自动清理了，所以可以强制清理这个 tee_pobj
     */
    pobj->refcnt = 0;  /* 强制清零引用计数 */

    /* 删除存储文件 - 直接调用 Trusty 存储接口 */
    ret = trusty_pobj_remove(pobj);
    if (ret != TEE_SUCCESS) {
        /* 即使删除文件失败，也要清理内存中的对象 */
        /* 因为 TA 已经 panic，不能留下孤儿对象 */
    }

    /* 从该实例的对象链表中移除 */
    list_delete(&pobj->link);
    ta_node->pobj_count--;

    mutex_release(&pobj->pobj_lock);

    /* 清理对象资源 */
    if (pobj->obj_id) {
        free(pobj->obj_id);
    }
    mutex_destroy(&pobj->pobj_lock);
    free(pobj);

    return ret;
}

/**
 * 处理清理通知 - 存储 TA 的消息处理函数（双重链表版本）
 * @param notification: 清理通知消息
 * @return: 成功返回 0，失败返回负数错误码
 */
int handle_cleanup_notification(const struct ta_cleanup_notification *notification) {
    struct storage_ta_context *storage_ctx;
    int ret;

    if (!notification) {
        return -EINVAL;
    }

    /* 获取存储 TA 上下文 */
    storage_ctx = get_storage_ta_context();
    if (!storage_ctx) {
        return -ENOENT;
    }

    /* 根据清理标志执行不同的清理操作 */
    if (notification->cleanup_flags & CLEANUP_FLAG_ALL_INSTANCES) {
        /* 清理该 TA 的所有实例 */
        ret = cleanup_ta_all_instances(storage_ctx, &notification->ta_uuid);
    } else {
        /* 清理指定的 TA 实例 */
        ret = cleanup_ta_instance_persistent_objects(storage_ctx,
                                                    &notification->ta_uuid,
                                                    notification->instance_id);
    }

    return ret;
}
```

## 6. GP API 接口层

### 6.1 完整 GP 存储 API 设计

基于 OP-TEE 的设计，实现完整的 GP 存储 API 接口（26个核心函数），上层调用的 API 是 GP 对应的接口。

#### 6.1.1 GP 存储 API 完整列表

**Generic Object Functions (5个)：**
```c
TEE_Result TEE_GetObjectInfo1(TEE_ObjectHandle object, TEE_ObjectInfo *objectInfo);
TEE_Result TEE_RestrictObjectUsage1(TEE_ObjectHandle object, uint32_t objectUsage);
uint32_t TEE_GetObjectBufferAttribute(TEE_ObjectHandle object, uint32_t attributeID, void *buffer, uint32_t *size);
uint32_t TEE_GetObjectValueAttribute(TEE_ObjectHandle object, uint32_t attributeID, uint32_t *a, uint32_t *b);
void TEE_CloseObject(TEE_ObjectHandle object);
```

**Transient Object Functions (7个)：**
```c
TEE_Result TEE_AllocateTransientObject(uint32_t objectType, uint32_t maxObjectSize, TEE_ObjectHandle *object);
void TEE_FreeTransientObject(TEE_ObjectHandle object);
void TEE_ResetTransientObject(TEE_ObjectHandle object);
TEE_Result TEE_PopulateTransientObject(TEE_ObjectHandle object, const TEE_Attribute *attrs, uint32_t attrCount);
void TEE_InitRefAttribute(TEE_Attribute *attr, uint32_t attributeID, const void *buffer, uint32_t length);
void TEE_InitValueAttribute(TEE_Attribute *attr, uint32_t attributeID, uint32_t a, uint32_t b);
void TEE_CopyObjectAttributes1(TEE_ObjectHandle destObject, TEE_ObjectHandle srcObject);
```

**Persistent Object Functions (5个)：**
```c
TEE_Result TEE_OpenPersistentObject(uint32_t storageID, const void *objectID, uint32_t objectIDLen, uint32_t flags, TEE_ObjectHandle *object);
TEE_Result TEE_CreatePersistentObject(uint32_t storageID, const void *objectID, uint32_t objectIDLen, uint32_t flags, TEE_ObjectHandle attributes, const void *initialData, uint32_t initialDataLen, TEE_ObjectHandle *object);
void TEE_CloseAndDeletePersistentObject1(TEE_ObjectHandle object);
TEE_Result TEE_RenamePersistentObject(TEE_ObjectHandle object, const void *newObjectID, uint32_t newObjectIDLen);
TEE_Result TEE_AllocatePersistentObjectEnumerator(TEE_ObjectEnumHandle *objectEnumerator);
```

**Persistent Object Enumeration (5个)：**
```c
void TEE_FreePersistentObjectEnumerator(TEE_ObjectEnumHandle objectEnumerator);
void TEE_ResetPersistentObjectEnumerator(TEE_ObjectEnumHandle objectEnumerator);
TEE_Result TEE_StartPersistentObjectEnumerator(TEE_ObjectEnumHandle objectEnumerator, uint32_t storageID);
TEE_Result TEE_GetNextPersistentObject(TEE_ObjectEnumHandle objectEnumerator, TEE_ObjectInfo *objectInfo, void *objectID, uint32_t *objectIDLen);
```

**Data Stream Access Functions (4个)：**
```c
TEE_Result TEE_ReadObjectData(TEE_ObjectHandle object, void *buffer, uint32_t size, uint32_t *count);
TEE_Result TEE_WriteObjectData(TEE_ObjectHandle object, const void *buffer, uint32_t size);
TEE_Result TEE_TruncateObjectData(TEE_ObjectHandle object, uint32_t size);
TEE_Result TEE_SeekObjectData(TEE_ObjectHandle object, int32_t offset, TEE_Whence whence);
```

#### 6.1.2 瞬时对象 busy 状态机制分析

**关键问题：瞬时对象在用户层 TA 管理后是否还需要 busy 状态？**

**答案：是的，仍然需要 busy 状态机制，原因如下：**

1. **GP 标准兼容性要求**：GP 标准明确定义了对象的并发访问控制
2. **OP-TEE 设计一致性**：保持与 OP-TEE 完全一致的并发控制机制
3. **多线程安全保护**：即使在用户空间，TA 仍可能有多线程访问同一对象
4. **操作原子性保证**：确保对象操作的原子性，防止数据竞争

**busy 状态使用场景：**
```c
/* 场景1：数据流操作期间防止并发访问 */
TEE_Result TEE_ReadObjectData(TEE_ObjectHandle object, void *buffer, uint32_t size, uint32_t *count) {
    struct trusty_tee_obj *obj;
    TEE_Result res;

    res = utee_obj_get(object);
    if (res != TEE_SUCCESS) return res;

    /* 设置 busy 状态，防止其他线程同时操作 */
    res = utee_obj_set_busy(obj);
    if (res != TEE_SUCCESS) return res;

    /* 执行读取操作 */
    res = perform_read_operation(obj, buffer, size, count);

    /* 清除 busy 状态 */
    utee_obj_clear_busy(obj);
    return res;
}

/* 场景2：属性操作期间防止并发修改 */
TEE_Result TEE_PopulateTransientObject(TEE_ObjectHandle object, const TEE_Attribute *attrs, uint32_t attrCount) {
    struct trusty_tee_obj *obj;
    TEE_Result res;

    res = utee_obj_get(object);
    if (res != TEE_SUCCESS) return res;

    /* 设置 busy 状态，防止属性被并发修改 */
    res = utee_obj_set_busy(obj);
    if (res != TEE_SUCCESS) return res;

    /* 执行属性填充操作 */
    res = populate_object_attributes(obj, attrs, attrCount);

    /* 清除 busy 状态 */
    utee_obj_clear_busy(obj);
    return res;
}
```

#### 6.1.3 GP 存储 API 核心实现（基于 libutee 库）

```c
/**
 * TEE_AllocateTransientObject - 分配瞬时对象
 * @param objectType: 对象类型
 * @param maxObjectSize: 最大对象大小
 * @param object: 输出的对象句柄
 * @return: TEE_Result
 */
TEE_Result TEE_AllocateTransientObject(uint32_t objectType,
                                      uint32_t maxObjectSize,
                                      TEE_ObjectHandle *object) {
    struct trusty_tee_obj *obj;

    if (!object) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 验证对象类型 */
    if (!utee_is_valid_object_type(objectType)) {
        return TEE_ERROR_NOT_SUPPORTED;
    }

    /* 通过 libutee 库分配 tee_obj */
    obj = utee_obj_alloc();
    if (!obj) {
        return TEE_ERROR_OUT_OF_MEMORY;
    }

    /* 初始化对象信息 */
    obj->info.objectType = objectType;
    obj->info.maxObjectSize = maxObjectSize;
    obj->info.objectSize = 0;
    obj->info.objectUsage = 0xFFFFFFFF;  /* 瞬时对象默认所有权限 */
    obj->info.dataSize = 0;
    obj->info.dataPosition = 0;
    obj->info.handleFlags = 0;

    /* 返回对象地址作为 handle */
    *object = (TEE_ObjectHandle)obj;
    return TEE_SUCCESS;
}

/**
 * TEE_FreeTransientObject - 释放瞬时对象
 * @param object: 对象句柄
 */
void TEE_FreeTransientObject(TEE_ObjectHandle object) {
    struct trusty_tee_obj *obj;

    /* 验证 handle */
    obj = utee_obj_get(object);
    if (!obj) {
        return;
    }

    /* 检查是否为瞬时对象 */
    if (obj->pobj) {
        /* 持久对象不能通过此函数释放 */
        return;
    }

    /* 释放对象 */
    utee_obj_free(obj);
}

/**
 * TEE_ResetTransientObject - 重置瞬时对象
 * @param object: 对象句柄
 */
void TEE_ResetTransientObject(TEE_ObjectHandle object) {
    struct trusty_tee_obj *obj;
    TEE_Result res;

    /* 验证 handle */
    obj = utee_obj_get(object);
    if (!obj) {
        return;
    }

    /* 检查是否为瞬时对象 */
    if (obj->pobj) {
        /* 持久对象不能重置 */
        return;
    }

    /* 设置 busy 状态 */
    res = utee_obj_set_busy(obj);
    if (res != TEE_SUCCESS) {
        return;
    }

    /* 清除属性和数据 */
    if (obj->attr) {
        secure_memset(obj->attr, 0, obj->have_attrs * sizeof(TEE_Attribute));
        free(obj->attr);
        obj->attr = NULL;
    }
    obj->have_attrs = 0;
    obj->info.objectSize = 0;
    obj->info.dataSize = 0;
    obj->info.dataPosition = 0;

    /* 清除 busy 状态 */
    utee_obj_clear_busy(obj);
}

/**
 * TEE_PopulateTransientObject - 用属性填充瞬时对象
 * @param object: 对象句柄
 * @param attrs: 属性数组
 * @param attrCount: 属性数量
 * @return: TEE_Result
 */
TEE_Result TEE_PopulateTransientObject(TEE_ObjectHandle object,
                                      const TEE_Attribute *attrs,
                                      uint32_t attrCount) {
    struct trusty_tee_obj *obj;
    TEE_Result res;

    /* 验证参数 */
    if (!attrs && attrCount > 0) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 验证 handle */
    obj = utee_obj_get(object);
    if (!obj) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 检查是否为瞬时对象 */
    if (obj->pobj) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 设置 busy 状态 */
    res = utee_obj_set_busy(obj);
    if (res != TEE_SUCCESS) {
        return res;
    }

    /* 复制属性 */
    res = utee_obj_copy_attributes(obj, attrs, attrCount);
    if (res != TEE_SUCCESS) {
        utee_obj_clear_busy(obj);
        return res;
    }

    /* 更新对象大小 */
    obj->info.objectSize = utee_obj_calculate_size(obj);

    /* 清除 busy 状态 */
    utee_obj_clear_busy(obj);
    return TEE_SUCCESS;
}

/**
 * TEE_OpenPersistentObject - 打开持久对象
 * @param storageID: 存储 ID
 * @param objectID: 对象 ID
 * @param objectIDLen: 对象 ID 长度
 * @param flags: 访问标志
 * @param object: 输出的对象句柄
 * @return: TEE_Result
 */
TEE_Result TEE_OpenPersistentObject(uint32_t storageID,
                                   const void *objectID, uint32_t objectIDLen,
                                   uint32_t flags,
                                   TEE_ObjectHandle *object) {
    struct trusty_tee_obj *obj;
    struct uuid ta_uuid;
    TEE_Result ret;

    if (!objectID || objectIDLen == 0 || !object) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 验证存储 ID */
    if (!utee_is_valid_storage_id(storageID)) {
        return TEE_ERROR_ITEM_NOT_FOUND;
    }

    /* 验证访问标志 */
    if (!utee_is_valid_access_flags(flags)) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 获取当前 TA UUID */
    get_current_ta_uuid(&ta_uuid);

    /* 通过 libutee 库分配 tee_obj */
    obj = utee_obj_alloc();
    if (!obj) {
        return TEE_ERROR_OUT_OF_MEMORY;
    }

    /* 连接存储 TA */
    ret = connect(STORAGE_TA_PORT, IPC_CONNECT_WAIT_FOR_PORT);
    if (ret < 0) {
        utee_obj_free(obj);
        return TEE_ERROR_COMMUNICATION;
    }
    obj->storage_handle = (handle_t)ret;

    /* 通过 IPC 请求打开持久对象 */
    ret = utee_storage_open_object(obj->storage_handle, &ta_uuid,
                                  objectID, objectIDLen, flags, &obj->pobj);
    if (ret != TEE_SUCCESS) {
        close(obj->storage_handle);
        utee_obj_free(obj);
        return ret;
    }

    /* 设置对象信息 */
    obj->info.handleFlags = flags;
    obj->info.objectType = obj->pobj->object_type;
    obj->info.dataSize = obj->pobj->data_size;
    obj->info.dataPosition = 0;

    /* 返回对象地址作为 handle */
    *object = (TEE_ObjectHandle)obj;
    return TEE_SUCCESS;
}

/**
 * TEE_CreatePersistentObject - 创建持久对象
 * @param storageID: 存储 ID
 * @param objectID: 对象 ID
 * @param objectIDLen: 对象 ID 长度
 * @param flags: 访问标志
 * @param attributes: 对象属性
 * @param initialData: 初始数据
 * @param initialDataLen: 初始数据长度
 * @param object: 输出的对象句柄
 * @return: TEE_Result
 */
TEE_Result TEE_CreatePersistentObject(uint32_t storageID,
                                     const void *objectID, uint32_t objectIDLen,
                                     uint32_t flags,
                                     TEE_ObjectHandle attributes,
                                     const void *initialData, uint32_t initialDataLen,
                                     TEE_ObjectHandle *object) {
    struct trusty_tee_obj *obj;
    struct uuid ta_uuid;
    TEE_Result ret;

    if (!objectID || objectIDLen == 0) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 获取当前 TA UUID */
    get_current_ta_uuid(&ta_uuid);

    /* 通过 libutee 库分配 tee_obj */
    obj = utee_obj_alloc();
    if (!obj) {
        return TEE_ERROR_OUT_OF_MEMORY;
    }

    /* 连接存储 TA */
    ret = connect(STORAGE_TA_PORT, IPC_CONNECT_WAIT_FOR_PORT);
    if (ret < 0) {
        utee_obj_free(obj);
        return TEE_ERROR_COMMUNICATION;
    }
    obj->storage_handle = (handle_t)ret;

    /* 通过 IPC 请求创建持久对象 */
    ret = utee_storage_create_object(obj->storage_handle, &ta_uuid,
                                    objectID, objectIDLen, flags,
                                    initialData, initialDataLen, &obj->pobj);
    if (ret != TEE_SUCCESS) {
        close(obj->storage_handle);
        utee_obj_free(obj);
        return ret;
    }

    /* 设置对象信息 */
    obj->info.dataSize = initialDataLen;
    obj->info.handleFlags = flags;

    /* 返回对象地址作为 handle */
    if (object) {
        *object = (TEE_ObjectHandle)obj;
    }

    return TEE_SUCCESS;
}

/**
 * TEE_CloseObject - 关闭对象
 * @param object: 对象句柄
 */
void TEE_CloseObject(TEE_ObjectHandle object) {
    struct trusty_tee_obj *obj;

    /* 验证 handle */
    obj = utee_obj_get(object);
    if (!obj) {
        return;
    }

    /* 关闭存储连接 */
    if (obj->storage_handle != INVALID_IPC_HANDLE) {
        close(obj->storage_handle);
    }

    /* 释放对象 */
    utee_obj_free(obj);
}

/**
 * TEE_CloseAndDeletePersistentObject1 - 关闭并删除持久对象
 * @param object: 对象句柄
 */
void TEE_CloseAndDeletePersistentObject1(TEE_ObjectHandle object) {
    struct trusty_tee_obj *obj;
    TEE_Result res;

    /* 验证 handle */
    obj = utee_obj_get(object);
    if (!obj) {
        return;
    }

    /* 检查是否为持久对象 */
    if (!obj->pobj) {
        /* 瞬时对象不能删除 */
        return;
    }

    /* 检查写权限 */
    if (!(obj->info.handleFlags & TEE_DATA_FLAG_ACCESS_WRITE)) {
        return;
    }

    /* 设置 busy 状态 */
    res = utee_obj_set_busy(obj);
    if (res != TEE_SUCCESS) {
        return;
    }

    /* 通过 IPC 请求删除持久对象 */
    utee_storage_delete_object(obj->storage_handle, obj->pobj);

    /* 清除 busy 状态 */
    utee_obj_clear_busy(obj);

    /* 关闭对象 */
    TEE_CloseObject(object);
}

/**
 * TEE_ReadObjectData - 读取对象数据
 * @param object: 对象句柄
 * @param buffer: 数据缓冲区
 * @param size: 读取大小
 * @param count: 实际读取的字节数
 * @return: TEE_Result
 */
TEE_Result TEE_ReadObjectData(TEE_ObjectHandle object, void *buffer,
                             uint32_t size, uint32_t *count) {
    struct trusty_tee_obj *obj;
    TEE_Result res;
    uint32_t bytes_read = 0;

    if (!buffer || !count) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 验证 handle */
    obj = utee_obj_get(object);
    if (!obj) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 设置 busy 状态 */
    res = utee_obj_set_busy(obj);
    if (res != TEE_SUCCESS) {
        return res;
    }

    *count = 0;

    /* 检查读权限 */
    if (!(obj->info.objectUsage & TEE_USAGE_EXTRACTABLE)) {
        utee_obj_clear_busy(obj);
        return TEE_ERROR_ACCESS_DENIED;
    }

    /* 瞬时对象没有数据流 */
    if (!obj->pobj) {
        utee_obj_clear_busy(obj);
        return TEE_SUCCESS;
    }

    /* 检查数据位置 */
    if (obj->ds_pos >= obj->info.dataSize) {
        utee_obj_clear_busy(obj);
        return TEE_SUCCESS;
    }

    /* 调整读取大小 */
    if (obj->ds_pos + size > obj->info.dataSize) {
        size = obj->info.dataSize - obj->ds_pos;
    }

    /* 通过 IPC 读取数据 */
    res = utee_storage_read_data(obj->storage_handle, obj->pobj,
                                obj->ds_pos, buffer, size, &bytes_read);
    if (res == TEE_SUCCESS) {
        obj->ds_pos += bytes_read;
        obj->info.dataPosition = obj->ds_pos;
        *count = bytes_read;
    }

    /* 清除 busy 状态 */
    utee_obj_clear_busy(obj);
    return res;
}

/**
 * TEE_WriteObjectData - 写入对象数据
 * @param object: 对象句柄
 * @param buffer: 数据缓冲区
 * @param size: 写入大小
 * @return: TEE_Result
 */
TEE_Result TEE_WriteObjectData(TEE_ObjectHandle object, const void *buffer,
                              uint32_t size) {
    struct trusty_tee_obj *obj;
    TEE_Result res;

    if (!buffer && size > 0) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 验证 handle */
    obj = utee_obj_get(object);
    if (!obj) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 设置 busy 状态 */
    res = utee_obj_set_busy(obj);
    if (res != TEE_SUCCESS) {
        return res;
    }

    /* 检查写权限 */
    if (!(obj->info.handleFlags & TEE_DATA_FLAG_ACCESS_WRITE)) {
        utee_obj_clear_busy(obj);
        return TEE_ERROR_ACCESS_DENIED;
    }

    /* 瞬时对象不支持数据流写入 */
    if (!obj->pobj) {
        utee_obj_clear_busy(obj);
        return TEE_ERROR_NOT_SUPPORTED;
    }

    /* 通过 IPC 写入数据 */
    res = utee_storage_write_data(obj->storage_handle, obj->pobj,
                                 obj->ds_pos, buffer, size);
    if (res == TEE_SUCCESS) {
        obj->ds_pos += size;
        obj->info.dataPosition = obj->ds_pos;

        /* 更新数据大小 */
        if (obj->ds_pos > obj->info.dataSize) {
            obj->info.dataSize = obj->ds_pos;
        }
    }

    /* 清除 busy 状态 */
    utee_obj_clear_busy(obj);
    return res;
}

### 6.2 libutee 库存储操作接口

libutee 库提供完整的存储操作接口，通过 IPC 与存储 TA 通信，实现对持久对象的管理。

#### 6.2.1 存储操作消息格式

```c
/* 存储操作命令定义 */
enum storage_cmd {
    STORAGE_CMD_OPEN_OBJECT = 1,
    STORAGE_CMD_CREATE_OBJECT,
    STORAGE_CMD_DELETE_OBJECT,
    STORAGE_CMD_READ_DATA,
    STORAGE_CMD_WRITE_DATA,
    STORAGE_CMD_TRUNCATE_DATA,
    STORAGE_CMD_SEEK_DATA,
    STORAGE_CMD_ENUM_START,
    STORAGE_CMD_ENUM_NEXT,
    STORAGE_CMD_ENUM_CLOSE,
};

/* 打开对象请求 */
struct storage_open_req {
    uint32_t cmd;                    /* STORAGE_CMD_OPEN_OBJECT */
    struct uuid ta_uuid;             /* TA UUID */
    uint32_t storage_id;             /* 存储 ID */
    uint32_t obj_id_len;             /* 对象 ID 长度 */
    uint32_t flags;                  /* 访问标志 */
    /* 后跟 obj_id 数据 */
};

/* 打开对象响应 */
struct storage_open_resp {
    TEE_Result result;               /* 操作结果 */
    uint64_t pobj_handle;            /* 持久对象句柄 */
    uint32_t object_type;            /* 对象类型 */
    uint32_t data_size;              /* 数据大小 */
    uint32_t object_usage;           /* 对象权限 */
};

/* 读取数据请求 */
struct storage_read_req {
    uint32_t cmd;                    /* STORAGE_CMD_READ_DATA */
    uint64_t pobj_handle;            /* 持久对象句柄 */
    uint32_t offset;                 /* 读取偏移 */
    uint32_t size;                   /* 读取大小 */
};

/* 读取数据响应 */
struct storage_read_resp {
    TEE_Result result;               /* 操作结果 */
    uint32_t bytes_read;             /* 实际读取字节数 */
    /* 后跟数据内容 */
};

/* 写入数据请求 */
struct storage_write_req {
    uint32_t cmd;                    /* STORAGE_CMD_WRITE_DATA */
    uint64_t pobj_handle;            /* 持久对象句柄 */
    uint32_t offset;                 /* 写入偏移 */
    uint32_t size;                   /* 写入大小 */
    /* 后跟数据内容 */
};

/* 写入数据响应 */
struct storage_write_resp {
    TEE_Result result;               /* 操作结果 */
    uint32_t bytes_written;          /* 实际写入字节数 */
};
```

#### 6.2.2 libutee 库核心接口实现

```c
/**
 * 打开持久对象 - libutee 库实现
 * @param storage_handle: 存储 TA 连接句柄
 * @param ta_uuid: TA UUID
 * @param obj_id: 对象 ID
 * @param obj_id_len: 对象 ID 长度
 * @param flags: 访问标志
 * @param pobj: 输出的持久对象引用
 * @return: TEE_Result
 */
TEE_Result utee_storage_open_object(handle_t storage_handle,
                                   const struct uuid *ta_uuid,
                                   const void *obj_id, uint32_t obj_id_len,
                                   uint32_t flags,
                                   struct trusty_pobj **pobj) {
    struct storage_open_req req;
    struct storage_open_resp resp;
    ipc_msg_t msg;
    int ret;

    /* 准备请求消息 */
    req.cmd = STORAGE_CMD_OPEN_OBJECT;
    memcpy(&req.ta_uuid, ta_uuid, sizeof(*ta_uuid));
    req.storage_id = TEE_STORAGE_PRIVATE;  /* 默认私有存储 */
    req.obj_id_len = obj_id_len;
    req.flags = flags;

    /* 发送请求 */
    msg.num_iov = 2;
    msg.iov[0].iov_base = &req;
    msg.iov[0].iov_len = sizeof(req);
    msg.iov[1].iov_base = (void *)obj_id;
    msg.iov[1].iov_len = obj_id_len;
    msg.num_handles = 0;

    ret = send_msg(storage_handle, &msg);
    if (ret < 0) {
        return TEE_ERROR_COMMUNICATION;
    }

    /* 接收响应 */
    msg.num_iov = 1;
    msg.iov[0].iov_base = &resp;
    msg.iov[0].iov_len = sizeof(resp);
    msg.num_handles = 0;

    ret = read_msg(storage_handle, msg.iov[0].iov_base, msg.iov[0].iov_len);
    if (ret < 0) {
        return TEE_ERROR_COMMUNICATION;
    }

    if (resp.result != TEE_SUCCESS) {
        return resp.result;
    }

    /* 创建本地 pobj 引用 */
    *pobj = (struct trusty_pobj *)resp.pobj_handle;
    return TEE_SUCCESS;
}

/**
 * 读取对象数据 - libutee 库实现
 * @param storage_handle: 存储 TA 连接句柄
 * @param pobj: 持久对象引用
 * @param offset: 读取偏移
 * @param buffer: 数据缓冲区
 * @param size: 读取大小
 * @param bytes_read: 实际读取字节数
 * @return: TEE_Result
 */
TEE_Result utee_storage_read_data(handle_t storage_handle,
                                 struct trusty_pobj *pobj,
                                 uint32_t offset, void *buffer,
                                 uint32_t size, uint32_t *bytes_read) {
    struct storage_read_req req;
    struct storage_read_resp resp;
    ipc_msg_t msg;
    int ret;

    /* 准备请求消息 */
    req.cmd = STORAGE_CMD_READ_DATA;
    req.pobj_handle = (uint64_t)pobj;
    req.offset = offset;
    req.size = size;

    /* 发送请求 */
    msg.num_iov = 1;
    msg.iov[0].iov_base = &req;
    msg.iov[0].iov_len = sizeof(req);
    msg.num_handles = 0;

    ret = send_msg(storage_handle, &msg);
    if (ret < 0) {
        return TEE_ERROR_COMMUNICATION;
    }

    /* 接收响应头 */
    ret = read_msg(storage_handle, &resp, sizeof(resp));
    if (ret < 0) {
        return TEE_ERROR_COMMUNICATION;
    }

    if (resp.result != TEE_SUCCESS) {
        return resp.result;
    }

    /* 接收数据内容 */
    if (resp.bytes_read > 0) {
        ret = read_msg(storage_handle, buffer, resp.bytes_read);
        if (ret < 0) {
            return TEE_ERROR_COMMUNICATION;
        }
    }

    *bytes_read = resp.bytes_read;
    return TEE_SUCCESS;
}

/**
 * 写入对象数据 - libutee 库实现
 * @param storage_handle: 存储 TA 连接句柄
 * @param pobj: 持久对象引用
 * @param offset: 写入偏移
 * @param buffer: 数据缓冲区
 * @param size: 写入大小
 * @return: TEE_Result
 */
TEE_Result utee_storage_write_data(handle_t storage_handle,
                                  struct trusty_pobj *pobj,
                                  uint32_t offset, const void *buffer,
                                  uint32_t size) {
    struct storage_write_req req;
    struct storage_write_resp resp;
    ipc_msg_t msg;
    int ret;

    /* 准备请求消息 */
    req.cmd = STORAGE_CMD_WRITE_DATA;
    req.pobj_handle = (uint64_t)pobj;
    req.offset = offset;
    req.size = size;

    /* 发送请求 */
    msg.num_iov = 2;
    msg.iov[0].iov_base = &req;
    msg.iov[0].iov_len = sizeof(req);
    msg.iov[1].iov_base = (void *)buffer;
    msg.iov[1].iov_len = size;
    msg.num_handles = 0;

    ret = send_msg(storage_handle, &msg);
    if (ret < 0) {
        return TEE_ERROR_COMMUNICATION;
    }

    /* 接收响应 */
    ret = read_msg(storage_handle, &resp, sizeof(resp));
    if (ret < 0) {
        return TEE_ERROR_COMMUNICATION;
    }

    return resp.result;
}

/**
 * 删除持久对象 - libutee 库实现
 * @param storage_handle: 存储 TA 连接句柄
 * @param pobj: 持久对象引用
 * @return: TEE_Result
 */
TEE_Result utee_storage_delete_object(handle_t storage_handle,
                                     struct trusty_pobj *pobj) {
    struct storage_delete_req {
        uint32_t cmd;
        uint64_t pobj_handle;
    } req;
    struct storage_delete_resp {
        TEE_Result result;
    } resp;
    ipc_msg_t msg;
    int ret;

    /* 准备请求消息 */
    req.cmd = STORAGE_CMD_DELETE_OBJECT;
    req.pobj_handle = (uint64_t)pobj;

    /* 发送请求 */
    msg.num_iov = 1;
    msg.iov[0].iov_base = &req;
    msg.iov[0].iov_len = sizeof(req);
    msg.num_handles = 0;

    ret = send_msg(storage_handle, &msg);
    if (ret < 0) {
        return TEE_ERROR_COMMUNICATION;
    }

    /* 接收响应 */
    ret = read_msg(storage_handle, &resp, sizeof(resp));
    if (ret < 0) {
        return TEE_ERROR_COMMUNICATION;
    }

    return resp.result;
}

/**
 * 减少持久对象引用计数 - libutee 库实现
 * @param pobj: 持久对象引用
 */
void utee_storage_pobj_put(struct trusty_pobj *pobj) {
    /* 在实际实现中，这里应该通过 IPC 通知存储 TA 减少引用计数 */
    /* 为了简化，这里只是一个占位符 */
    (void)pobj;
}
```

### 6.3 GP API 属性管理接口

#### 6.3.1 属性操作辅助函数

```c
/**
 * TEE_GetObjectInfo1 - 获取对象信息
 * @param object: 对象句柄
 * @param objectInfo: 输出的对象信息
 * @return: TEE_Result
 */
TEE_Result TEE_GetObjectInfo1(TEE_ObjectHandle object, TEE_ObjectInfo *objectInfo) {
    struct trusty_tee_obj *obj;

    if (!objectInfo) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 验证 handle */
    obj = utee_obj_get(object);
    if (!obj) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 复制对象信息 */
    memcpy(objectInfo, &obj->info, sizeof(TEE_ObjectInfo));
    return TEE_SUCCESS;
}

/**
 * TEE_RestrictObjectUsage1 - 限制对象使用权限
 * @param object: 对象句柄
 * @param objectUsage: 新的使用权限
 * @return: TEE_Result
 */
TEE_Result TEE_RestrictObjectUsage1(TEE_ObjectHandle object, uint32_t objectUsage) {
    struct trusty_tee_obj *obj;
    TEE_Result res;

    /* 验证 handle */
    obj = utee_obj_get(object);
    if (!obj) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 设置 busy 状态 */
    res = utee_obj_set_busy(obj);
    if (res != TEE_SUCCESS) {
        return res;
    }

    /* 只能限制权限，不能扩展权限 */
    obj->info.objectUsage &= objectUsage;

    /* 清除 busy 状态 */
    utee_obj_clear_busy(obj);
    return TEE_SUCCESS;
}

/**
 * TEE_GetObjectBufferAttribute - 获取缓冲区属性
 * @param object: 对象句柄
 * @param attributeID: 属性 ID
 * @param buffer: 输出缓冲区
 * @param size: 缓冲区大小指针
 * @return: 属性标志
 */
uint32_t TEE_GetObjectBufferAttribute(TEE_ObjectHandle object, uint32_t attributeID,
                                     void *buffer, uint32_t *size) {
    struct trusty_tee_obj *obj;
    TEE_Attribute *attr;
    uint32_t i;

    if (!size) {
        return 0;
    }

    /* 验证 handle */
    obj = utee_obj_get(object);
    if (!obj) {
        return 0;
    }

    /* 查找属性 */
    for (i = 0; i < obj->have_attrs; i++) {
        attr = &((TEE_Attribute *)obj->attr)[i];
        if (attr->attributeID == attributeID) {
            /* 检查是否为缓冲区属性 */
            if (!(attr->attributeID & TEE_ATTR_FLAG_VALUE)) {
                uint32_t attr_size = attr->content.ref.length;

                if (buffer && *size >= attr_size) {
                    memcpy(buffer, attr->content.ref.buffer, attr_size);
                }
                *size = attr_size;
                return TEE_ATTR_FLAG_SET;
            }
        }
    }

    return 0;
}

/**
 * TEE_GetObjectValueAttribute - 获取值属性
 * @param object: 对象句柄
 * @param attributeID: 属性 ID
 * @param a: 输出值 A
 * @param b: 输出值 B
 * @return: 属性标志
 */
uint32_t TEE_GetObjectValueAttribute(TEE_ObjectHandle object, uint32_t attributeID,
                                    uint32_t *a, uint32_t *b) {
    struct trusty_tee_obj *obj;
    TEE_Attribute *attr;
    uint32_t i;

    /* 验证 handle */
    obj = utee_obj_get(object);
    if (!obj) {
        return 0;
    }

    /* 查找属性 */
    for (i = 0; i < obj->have_attrs; i++) {
        attr = &((TEE_Attribute *)obj->attr)[i];
        if (attr->attributeID == attributeID) {
            /* 检查是否为值属性 */
            if (attr->attributeID & TEE_ATTR_FLAG_VALUE) {
                if (a) *a = attr->content.value.a;
                if (b) *b = attr->content.value.b;
                return TEE_ATTR_FLAG_SET;
            }
        }
    }

    return 0;
}

/**
 * TEE_InitRefAttribute - 初始化引用属性
 * @param attr: 属性结构
 * @param attributeID: 属性 ID
 * @param buffer: 数据缓冲区
 * @param length: 数据长度
 */
void TEE_InitRefAttribute(TEE_Attribute *attr, uint32_t attributeID,
                         const void *buffer, uint32_t length) {
    if (!attr) {
        return;
    }

    attr->attributeID = attributeID;
    attr->content.ref.buffer = (void *)buffer;
    attr->content.ref.length = length;
}

/**
 * TEE_InitValueAttribute - 初始化值属性
 * @param attr: 属性结构
 * @param attributeID: 属性 ID
 * @param a: 值 A
 * @param b: 值 B
 */
void TEE_InitValueAttribute(TEE_Attribute *attr, uint32_t attributeID,
                           uint32_t a, uint32_t b) {
    if (!attr) {
        return;
    }

    attr->attributeID = attributeID | TEE_ATTR_FLAG_VALUE;
    attr->content.value.a = a;
    attr->content.value.b = b;
}

/**
 * TEE_CopyObjectAttributes1 - 复制对象属性
 * @param destObject: 目标对象句柄
 * @param srcObject: 源对象句柄
 */
void TEE_CopyObjectAttributes1(TEE_ObjectHandle destObject, TEE_ObjectHandle srcObject) {
    struct trusty_tee_obj *dest_obj, *src_obj;
    TEE_Result res;

    /* 验证 handle */
    dest_obj = utee_obj_get(destObject);
    src_obj = utee_obj_get(srcObject);
    if (!dest_obj || !src_obj) {
        return;
    }

    /* 检查是否为瞬时对象 */
    if (dest_obj->pobj || src_obj->pobj) {
        return;
    }

    /* 设置目标对象 busy 状态 */
    res = utee_obj_set_busy(dest_obj);
    if (res != TEE_SUCCESS) {
        return;
    }

    /* 复制属性 */
    if (src_obj->attr && src_obj->have_attrs > 0) {
        utee_obj_copy_attributes(dest_obj, (TEE_Attribute *)src_obj->attr, src_obj->have_attrs);
    }

    /* 复制对象信息 */
    dest_obj->info.objectType = src_obj->info.objectType;
    dest_obj->info.objectSize = src_obj->info.objectSize;
    dest_obj->info.maxObjectSize = src_obj->info.maxObjectSize;
    dest_obj->info.objectUsage = src_obj->info.objectUsage;

    /* 清除 busy 状态 */
    utee_obj_clear_busy(dest_obj);
}
```

## 7. 实现注意事项

### 7.1 关键技术点

在实现该设计方案时，需要特别注意以下关键技术点，确保系统的正确性和可靠性。

#### 7.1.1 瞬时对象 busy 状态的必要性

**核心结论：瞬时对象在用户层 TA 管理后仍然需要 busy 状态机制。**

**技术原因分析：**

1. **GP 标准合规性**：GP TEE Internal Core API 明确要求对象操作的原子性保护
2. **多线程安全**：即使在用户空间，TA 可能存在多线程并发访问同一对象
3. **操作一致性**：确保属性修改、数据操作等复合操作的原子性
4. **OP-TEE 兼容性**：保持与 OP-TEE 完全一致的并发控制语义

**具体使用场景：**
- 属性填充操作期间防止并发修改
- 对象信息更新期间防止并发访问
- 数据流操作期间防止状态不一致
- 对象重置操作期间防止并发干扰

#### 7.1.2 OP-TEE vs Trusty 的关键差异

**OP-TEE 模型：**
- tee_obj 和 tee_pobj 都在内核中
- 内核维护 TA 上下文中的对象链表
- TA panic 时需要内核主动清理所有对象

**Trusty 模型：**
- tee_obj 在 TA 用户空间的 libutee 库中
- tee_pobj 在存储 TA 中，统一管理
- TA panic 时，libutee 库中的 tee_obj 自动清理，只需清理存储 TA 中的 tee_pobj

**设计适配要点：**
- 保持 OP-TEE 的对象语义和 API 兼容性
- 使用 libutee 库管理 tee_obj，保持链表结构
- 通过 IPC 与存储 TA 通信，管理 tee_pobj
- **双重链表管理**：第一层按 TA UUID + 实例号管理，第二层管理具体持久对象
- **简化存储操作**：tee_pobj 在存储 TA 中直接调用 Trusty 存储接口，无需 fops 抽象层
- **用户对象 ID 保真**：完全以用户指定的对象 ID 为准，系统不做任何修改
- **精确 panic 清理**：可以精确清理指定 TA 实例的所有持久对象

#### 7.1.3 双重链表设计优势

**相比单层链表的优势：**

1. **多实例支持**：天然支持多实例 TA，每个实例独立管理
2. **精确清理**：panic 时可以精确清理指定实例，不影响其他实例
3. **性能优化**：查找对象时只需遍历对应实例的对象链表，减少查找范围
4. **内存局部性**：同一实例的对象在内存中相对集中，提高缓存效率
5. **并发优化**：不同实例的对象操作可以并行，减少锁竞争

**双重链表结构对比：**

| 方面 | 单层链表 | 双重链表 |
|------|----------|----------|
| **实例支持** | 需要额外设计 | 天然支持多实例 |
| **panic 清理** | 遍历全部对象 | 只清理指定实例 |
| **查找性能** | O(n) 全局查找 | O(m) 实例内查找 |
| **并发性能** | 全局锁竞争 | 实例级锁隔离 |
| **内存管理** | 全局分散 | 实例内聚合 |

#### 7.1.4 存储操作简化设计优势

**取消 fops 抽象层的优势：**

1. **简化架构**：减少一层函数指针抽象，降低系统复杂度
2. **提高性能**：直接调用存储接口，减少函数调用开销
3. **降低内存占用**：每个 tee_pobj 节省一个指针字段
4. **简化维护**：减少接口适配代码，降低维护成本
5. **更好的类型安全**：直接调用具体函数，编译时类型检查更严格

**实现方式对比：**

| 方面 | OP-TEE 方式 | Trusty 简化方式 |
|------|-------------|-----------------|
| **存储操作** | `pobj->fops->read()` | `trusty_pobj_read()` |
| **函数调用层次** | 3层（API→fops→实现） | 2层（API→实现） |
| **内存占用** | 每个 pobj 8字节指针 | 节省 8字节 |
| **编译优化** | 间接调用，优化受限 | 直接调用，更好优化 |
| **调试便利性** | 需要跟踪函数指针 | 直接函数调用栈 |

#### 7.1.5 关于 storage_session_t 的设计决策

**问题分析：**
- `storage_session_t session` 字段在原设计中没有明确的初始化和使用方式
- 在 Trusty 环境中，存储操作通常通过全局的存储服务连接进行
- 每个 pobj 单独维护 session 可能导致资源浪费和管理复杂性

**设计决策：移除 pobj 中的 session 字段**

**理由：**
1. **全局连接更高效**：存储 TA 使用全局的 `storage_service_handle` 连接存储服务
2. **简化资源管理**：避免每个对象单独管理连接，减少资源泄漏风险
3. **降低内存占用**：每个 pobj 节省 session 相关的内存开销
4. **统一连接管理**：所有存储操作通过统一的连接进行，便于监控和管理

**最终 pobj 结构优化：**
- 移除 `storage_session_t session` 字段
- 存储操作通过 `storage_ta_context` 中的全局 `storage_service_handle` 进行
- 简化了对象初始化和清理流程

#### 7.1.2 并发控制

```c
/* 并发控制最佳实践 - 基于 libutee 库 */

/* 1. libutee 库锁的获取顺序 - 避免死锁 */
void acquire_utee_locks_safely(struct trusty_tee_obj *obj1,
                               struct trusty_tee_obj *obj2) {
    /* 总是先获取全局锁，再获取对象锁 */
    mutex_acquire(&g_utee_obj_ctx.objects_lock);

    /* 按地址顺序获取对象锁，避免死锁 */
    if (obj1 < obj2) {
        mutex_acquire(&obj1->obj_lock);
        if (obj2) {
            mutex_acquire(&obj2->obj_lock);
        }
    } else {
        if (obj2) {
            mutex_acquire(&obj2->obj_lock);
        }
        mutex_acquire(&obj1->obj_lock);
    }
}

/* 2. 引用计数的原子操作 - 通过 IPC 实现 */
bool atomic_increment_pobj_refcnt(struct trusty_pobj *pobj) {
    /* 在 Trusty 中，引用计数操作通过 IPC 发送给存储 TA */
    /* 存储 TA 负责原子性地处理引用计数 */
    return utee_storage_pobj_get(pobj);
}

/* 3. busy 标志的使用 - 基于 OP-TEE 逻辑 */
TEE_Result set_object_busy_safe(struct trusty_tee_obj *obj) {
    return utee_obj_set_busy(obj);
}
```

#### 7.1.3 内存管理

```c
/* 内存管理最佳实践 - 基于 libutee 库 */

/* 1. 安全的对象分配 - 使用 libutee 库 */
struct trusty_tee_obj *safe_obj_alloc(void) {
    return utee_obj_alloc();
}

/* 2. 安全的对象释放 - 使用 libutee 库 */
void safe_obj_free(struct trusty_tee_obj *obj) {
    utee_obj_free(obj);
}

/* 3. libutee 库的内存管理 */
void utee_memory_management_best_practices(void) {
    /*
     * libutee 库内存管理要点：
     *
     * 1. 对象分配时检查资源限制
     * 2. 对象释放时安全清除敏感数据
     * 3. 使用链表统一管理所有对象
     * 4. TA 退出时自动清理所有资源
     * 5. 通过 IPC 与存储 TA 通信，避免直接内存访问
     */
}

/* 4. 安全内存清除 - 防止信息泄露 */
void secure_memset(void *ptr, int value, size_t size) {
    volatile uint8_t *p = (volatile uint8_t *)ptr;
    while (size--) {
        *p++ = value;
    }
    /* 确保编译器不会优化掉这个操作 */
    __asm__ __volatile__("" : : "r"(ptr) : "memory");
}

/* 5. IPC 通信的内存管理 */
int safe_ipc_communication(handle_t handle, const void *req, size_t req_size,
                          void *resp, size_t resp_size) {
    ipc_msg_t msg;
    int ret;

    /* 准备发送消息 */
    msg.num_iov = 1;
    msg.iov[0].iov_base = (void *)req;
    msg.iov[0].iov_len = req_size;
    msg.num_handles = 0;

    ret = send_msg(handle, &msg);
    if (ret < 0) {
        return ret;
    }

    /* 接收响应 */
    ret = read_msg(handle, resp, resp_size);
    if (ret < 0) {
        /* 清除可能的敏感数据 */
        secure_memset(resp, 0, resp_size);
        return ret;
    }

    return 0;
}
```

### 7.2 兼容性考虑

#### 7.2.1 与现有系统的兼容性

**Trusty 存储服务兼容性：**
- 确保新的对象管理机制不影响现有的文件操作接口
- 保持与现有存储端口的兼容性
- 维护现有的事务处理和错误处理机制

**GP API 兼容性：**
- 完全实现 GP 标准的存储 API 接口
- 保持与 OP-TEE 的 API 兼容性
- 确保错误码和返回值的一致性

#### 7.2.2 性能影响评估

**内存使用影响：**
- 新增的对象管理结构预计增加内存使用约 1-3MB
- 通过引用计数机制减少内存占用
- 实施内存使用监控，确保不超过系统限制

**存储性能影响：**
- 基于 OP-TEE 成熟设计，性能影响最小
- 通过存储 TA 统一管理，提高缓存效率
- 实施性能基准测试，确保满足性能要求

### 7.3 设计限制说明

#### 7.3.1 多实例支持限制

**当前限制：**
- 本设计方案主要针对单实例 TA 环境
- 多实例 TA 的对象隔离和资源管理需要额外的设计考虑

**后续版本规划：**
- 多实例支持将在 v2.0 版本中单独设计和实现
- 需要扩展对象标识机制，支持实例级别的隔离

#### 7.3.2 Panic 流程限制

**当前限制：**
- 本文档暂不涉及 TA panic 时的特殊处理流程
- Panic 状态下的资源清理策略需要特殊考虑

**后续版本规划：**
- Panic 流程将在 v2.0 版本中单独设计
- 需要设计 panic 状态下的快速资源清理策略

---

**文档版本：** v1.0 - 基于 OP-TEE 设计
**最后更新：** 2024年12月
**状态：** 设计完成，待实现

**核心设计原则：**
1. **完全基于 OP-TEE 架构**：tee_obj + tee_pobj 双层对象模型
2. **libutee 库实现**：tee_obj 在 libutee 库中管理，保持链表结构
3. **IPC 通信机制**：通过 IPC 与存储 TA 通信，管理 tee_pobj
4. **GP 标准兼容**：上层提供完整的 GP 存储 API 接口
5. **简化清理机制**：TA panic 时只需清理存储 TA 中的 tee_pobj

**实现要点：**
- tee_obj 由 libutee 库管理，使用链表数据结构
- tee_pobj 由存储 TA 统一管理，基于引用计数机制
- GP API 通过 libutee 库实现，与存储 TA 进行 IPC 通信
- TA panic 时，libutee 库中的对象自动清理，存储 TA 收到通知后清理 tee_pobj

### 7.4 GP API 完整性总结

#### 7.4.1 已设计的 GP API 列表（26个）

**Generic Object Functions (5个) - ✅ 完成：**
1. `TEE_GetObjectInfo1` - 获取对象信息
2. `TEE_RestrictObjectUsage1` - 限制对象使用权限
3. `TEE_GetObjectBufferAttribute` - 获取缓冲区属性
4. `TEE_GetObjectValueAttribute` - 获取值属性
5. `TEE_CloseObject` - 关闭对象

**Transient Object Functions (7个) - ✅ 完成：**
1. `TEE_AllocateTransientObject` - 分配瞬时对象
2. `TEE_FreeTransientObject` - 释放瞬时对象
3. `TEE_ResetTransientObject` - 重置瞬时对象
4. `TEE_PopulateTransientObject` - 用属性填充瞬时对象
5. `TEE_InitRefAttribute` - 初始化引用属性
6. `TEE_InitValueAttribute` - 初始化值属性
7. `TEE_CopyObjectAttributes1` - 复制对象属性

**Persistent Object Functions (5个) - ✅ 完成：**
1. `TEE_OpenPersistentObject` - 打开持久对象
2. `TEE_CreatePersistentObject` - 创建持久对象
3. `TEE_CloseAndDeletePersistentObject1` - 关闭并删除持久对象
4. `TEE_RenamePersistentObject` - 重命名持久对象（需补充）
5. `TEE_AllocatePersistentObjectEnumerator` - 分配持久对象枚举器（需补充）

**Persistent Object Enumeration (5个) - 🔄 需补充：**
1. `TEE_FreePersistentObjectEnumerator` - 释放持久对象枚举器
2. `TEE_ResetPersistentObjectEnumerator` - 重置持久对象枚举器
3. `TEE_StartPersistentObjectEnumerator` - 启动持久对象枚举器
4. `TEE_GetNextPersistentObject` - 获取下一个持久对象

**Data Stream Access Functions (4个) - ✅ 完成：**
1. `TEE_ReadObjectData` - 读取对象数据
2. `TEE_WriteObjectData` - 写入对象数据
3. `TEE_TruncateObjectData` - 截断对象数据（需补充）
4. `TEE_SeekObjectData` - 定位对象数据（需补充）

#### 7.4.2 瞬时对象 busy 状态机制结论

**最终结论：瞬时对象在用户层 TA 管理后仍然需要 busy 状态机制。**

**关键理由：**

1. **GP 标准合规性**：GP TEE Internal Core API v1.3.1 明确要求对象操作的并发控制
2. **OP-TEE 设计一致性**：完全保持与 OP-TEE 的并发控制语义一致
3. **多线程安全保护**：防止 TA 内部多线程并发访问同一对象导致的数据竞争
4. **操作原子性保证**：确保复合操作（如属性填充、对象重置）的原子性
5. **错误处理一致性**：保持 `TEE_ERROR_BUSY` 错误码的语义一致性

**实现要点：**
- 所有对象操作前都需要检查和设置 busy 状态
- 操作完成后必须清除 busy 状态
- busy 状态检查应该是原子操作，使用互斥锁保护
- 瞬时对象和持久对象都需要 busy 状态保护

**重要设计优化：** 本设计方案相比传统 OP-TEE 架构的重要优化包括：

1. **双重链表管理**：第一层按 TA UUID + 实例号管理，第二层管理具体持久对象，支持多实例和精确 panic 清理
2. **用户对象 ID 保真**：完全以用户指定的对象 ID 为准，系统不做任何修改或转换
3. **简化存储操作**：tee_pobj 在存储 TA 中直接调用 Trusty 存储接口，无需 fops 函数指针抽象层
4. **全局连接管理**：移除 pobj 中的 session 字段，使用全局存储服务连接，提高效率

**注意：** 本文档描述的是基于 OP-TEE 设计的可信存储方案，适配 Trusty 用户空间环境。设计包含完整的 26 个 GP 存储 API，其中 21 个已完成详细设计，5 个需要在后续实现中补充。瞬时对象的 busy 状态机制是必需的，确保了系统的并发安全性和 GP 标准兼容性。双重链表设计天然支持多实例 TA 和精确的 panic 清理机制。
#### 7.4.3 完整 GP 存储 API 架构图

```mermaid
graph TB
    subgraph "GP API 层 (26个函数)"
        A1[Generic Object Functions<br/>5个函数]
        A2[Transient Object Functions<br/>7个函数]
        A3[Persistent Object Functions<br/>5个函数]
        A4[Persistent Object Enumeration<br/>5个函数]
        A5[Data Stream Access Functions<br/>4个函数]
    end

    subgraph "libutee 库层"
        B1[tee_obj 管理]
        B2[属性处理]
        B3[并发控制 busy状态]
        B4[IPC 通信接口]
    end

    subgraph "存储 TA 层"
        C1[tee_pobj 管理]
        C2[引用计数控制]
        C3[直接存储操作]
        C4[TA 隔离机制]
    end

    subgraph "Trusty 存储服务"
        D1[文件操作]
        D2[数据持久化]
        D3[存储空间管理]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B1
    A4 --> B1
    A5 --> B1

    B1 --> B2
    B2 --> B3
    B3 --> B4

    B4 --> C1
    C1 --> C2
    C2 --> C3
    C3 --> C4

    C3 --> D1
    D1 --> D2
    D2 --> D3

    style A1 fill:#e1f5fe
    style A2 fill:#e8f5e8
    style A3 fill:#fff3e0
    style A4 fill:#fce4ec
    style A5 fill:#f3e5f5
```

#### 7.4.4 瞬时对象 busy 状态流程图

```mermaid
sequenceDiagram
    participant App as TA 应用
    participant API as GP API
    participant Obj as tee_obj
    participant Lock as busy 状态

    App->>API: 调用 GP API (如 TEE_PopulateTransientObject)
    API->>Obj: utee_obj_get(handle)
    Obj-->>API: 返回 tee_obj

    API->>Lock: utee_obj_set_busy(obj)
    alt busy 状态已设置
        Lock-->>API: 返回 TEE_ERROR_BUSY
        API-->>App: 返回 TEE_ERROR_BUSY
    else busy 状态未设置
        Lock-->>API: 设置 busy=true, 返回 TEE_SUCCESS

        API->>API: 执行实际操作<br/>(属性填充/数据操作等)

        API->>Lock: utee_obj_clear_busy(obj)
        Lock-->>API: 清除 busy=false

        API-->>App: 返回操作结果
    end
```
