# Trusty TEE 可信存储新设计方案

## 1. 概述

### 1.1 设计目标

本文档详细描述 Trusty TEE 项目中可信存储（trusted storage）的新设计方案，基于 OP-TEE 成熟的双层对象模型，在 Trusty 用户空间环境中实现 GP 标准的可信存储功能。

**核心设计目标：**
- **OP-TEE 架构适配**：完全基于 OP-TEE 的 tee_obj + tee_pobj 双层对象模型
- **用户空间实现**：所有对象管理在用户空间完成，避免内核修改
- **简化设计原则**：保持方案简洁，避免过度复杂化
- **GP 标准兼容**：上层提供完整的 GP 存储 API 接口

### 1.2 适用范围和限制

**适用范围：**
- 支持 GP 标准的瞬时对象（Transient Object）和持久化对象（Persistent Object）
- 适用于单实例 TA 环境下的存储需求
- 支持完整的 GP 存储 API 接口

**设计限制：**
- 多实例支持将在后续版本中单独设计，本文档暂不涉及
- panic 流程将在后续版本中单独设计，本文档暂不涉及
- 当前设计主要针对功能实现，性能优化将在后续迭代中完善

### 1.3 整体架构

基于 OP-TEE 设计原理，采用双层对象模型：

```mermaid
graph TB
    subgraph "GP API Layer"
        A1[TEE_OpenPersistentObject]
        A2[TEE_CreatePersistentObject]
        A3[TEE_AllocateTransientObject]
        A4[TEE_ReadObjectData]
        A5[TEE_WriteObjectData]
    end

    subgraph "TA 用户空间 - tee_obj 管理"
        B1[struct trusty_tee_obj]
        B2[libutee 库管理]
        B3[TA 对象链表]
    end

    subgraph "存储 TA - tee_pobj 管理"
        C1[struct trusty_pobj]
        C2[持久对象全局管理]
        C3[存储操作接口]
    end

    subgraph "Trusty 存储服务"
        D1[storage_open_file]
        D2[storage_read/write]
        D3[storage_delete_file]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B1
    A4 --> B1
    A5 --> B1

    B1 --> C1
    B2 --> B1
    B3 --> B1

    C1 --> D1
    C2 --> C1
    C3 --> C1

    D1 --> D2
    D2 --> D3
```

## 2. 瞬时对象/句柄管理

### 2.1 数据结构设计

基于 OP-TEE 的 tee_obj 设计，但适配 Trusty 用户空间环境。tee_obj 既是瞬时对象又是句柄，由 TA 在用户空间自主管理，无需内核维护链表。

#### 2.1.1 核心数据结构（基于 OP-TEE tee_obj，适配 Trusty）

```c
/* Trusty TEE 对象句柄 - 基于 OP-TEE tee_obj 设计，适配用户空间 */
struct trusty_tee_obj {
    /* 链表管理 - 在 libutee 库中维护 */
    struct list_node link;         /* TA 对象链表节点 */

    /* GP 标准对象信息 - 完全兼容 OP-TEE */
    TEE_ObjectInfo info;           /* GP 标准对象信息 */

    /* 并发控制 - 完全保持 OP-TEE 设计 */
    bool busy;                     /* 操作忙标志，防止并发操作 */

    /* 属性管理 - 完全保持 OP-TEE 设计 */
    uint32_t have_attrs;           /* 属性位字段，标识已设置的属性 */
    void *attr;                    /* 属性数据指针 */

    /* 数据流管理 - 完全保持 OP-TEE 设计 */
    size_t ds_pos;                 /* 数据流起始位置偏移 */

    /* 存储抽象 - 适配 Trusty 机制 */
    struct trusty_pobj *pobj;      /* 指向持久化对象的指针 */
    handle_t storage_handle;       /* 存储 TA 连接句柄 */

    /* Trusty 用户空间扩展 */
    mutex_t obj_lock;              /* 对象锁 */
};

/* libutee 库中的对象管理上下文 */
struct utee_object_context {
    struct list_node objects;      /* TA 对象链表头 */
    uint32_t object_count;         /* 当前对象数量 */
    uint32_t max_objects;          /* 最大对象数量限制 */
    mutex_t objects_lock;          /* 对象链表锁 */
};

/* 全局对象管理上下文（在 libutee 库中） */
extern struct utee_object_context g_utee_obj_ctx;
```

#### 2.1.2 OP-TEE vs Trusty 对比

| OP-TEE 字段 | Trusty TEE 字段 | 差异说明 |
|-------------|-----------------|----------|
| `TAILQ_ENTRY(tee_obj) link` | `struct list_node link` | 在 libutee 库中维护链表 |
| `TEE_ObjectInfo info` | `TEE_ObjectInfo info` | 完全保持 GP 标准对象信息 |
| `bool busy` | `bool busy` | 完全保持并发控制标志 |
| `uint32_t have_attrs` | `uint32_t have_attrs` | 完全保持属性位字段 |
| `void *attr` | `void *attr` | 完全保持属性数据指针 |
| `size_t ds_pos` | `size_t ds_pos` | 完全保持数据流位置 |
| `struct tee_pobj *pobj` | `struct trusty_pobj *pobj` | 适配 Trusty 持久对象 |
| `struct tee_file_handle *fh` | `handle_t storage_handle` | 适配 Trusty IPC 句柄 |

#### 2.1.3 Trusty 对象管理模型

```mermaid
graph TB
    subgraph "TA 用户空间"
        A[libutee 库]
        A --> B[对象链表管理]
        B --> C[tee_obj 1]
        B --> D[tee_obj 2]
        B --> E[tee_obj N]
        C --> F[存储 TA 连接]
        D --> G[存储 TA 连接]
        E --> H[存储 TA 连接]
    end

    subgraph "存储 TA"
        I[tee_pobj 全局管理]
        F --> I
        G --> I
        H --> I
    end

    subgraph "TA Panic 处理"
        J[TA Panic]
        J --> K[libutee 对象链表自动清理]
        J --> L[通知存储 TA 清理 tee_pobj]
    end
```

### 2.2 管理机制

TA 通过 libutee 库管理 tee_obj，使用链表数据结构。使用对象地址作为 handle，TA panic 时对象链表自动清理。

#### 2.2.1 对象分配操作（libutee 库实现）

```c
/**
 * 分配新的 tee_obj 对象 - libutee 库实现
 * @return: 成功返回对象指针（作为 handle），失败返回 NULL
 */
struct trusty_tee_obj *utee_obj_alloc(void) {
    struct trusty_tee_obj *obj;

    mutex_acquire(&g_utee_obj_ctx.objects_lock);

    /* 检查对象数量限制 */
    if (g_utee_obj_ctx.object_count >= g_utee_obj_ctx.max_objects) {
        mutex_release(&g_utee_obj_ctx.objects_lock);
        return NULL;
    }

    /* 分配对象结构 */
    obj = calloc(1, sizeof(*obj));
    if (!obj) {
        mutex_release(&g_utee_obj_ctx.objects_lock);
        return NULL;
    }

    /* 初始化对象 - 基于 OP-TEE 初始化逻辑 */
    list_initialize(&obj->link);
    memset(&obj->info, 0, sizeof(obj->info));
    obj->busy = false;
    obj->have_attrs = 0;
    obj->attr = NULL;
    obj->ds_pos = 0;
    obj->pobj = NULL;
    obj->storage_handle = INVALID_IPC_HANDLE;
    mutex_init(&obj->obj_lock);

    /* 添加到 libutee 的对象链表 */
    list_add_tail(&g_utee_obj_ctx.objects, &obj->link);
    g_utee_obj_ctx.object_count++;

    mutex_release(&g_utee_obj_ctx.objects_lock);

    /* 返回对象地址作为 handle */
    return obj;
}

/**
 * 释放 tee_obj 对象 - libutee 库实现
 * @param obj: 要释放的对象
 */
void utee_obj_free(struct trusty_tee_obj *obj) {
    if (!obj) {
        return;
    }

    mutex_acquire(&g_utee_obj_ctx.objects_lock);
    mutex_acquire(&obj->obj_lock);

    /* 清理资源 - 基于 OP-TEE 清理逻辑 */
    if (obj->pobj) {
        /* 通知存储 TA 减少持久对象的引用计数 */
        utee_storage_pobj_put(obj->pobj);
        obj->pobj = NULL;
    }

    if (obj->storage_handle != INVALID_IPC_HANDLE) {
        close(obj->storage_handle);
        obj->storage_handle = INVALID_IPC_HANDLE;
    }

    if (obj->attr) {
        /* 安全清除属性数据 */
        memset(obj->attr, 0, obj->have_attrs * sizeof(TEE_Attribute));
        free(obj->attr);
        obj->attr = NULL;
    }

    /* 从链表中移除 */
    list_delete(&obj->link);
    g_utee_obj_ctx.object_count--;

    mutex_release(&obj->obj_lock);
    mutex_destroy(&obj->obj_lock);

    /* 清除对象结构 */
    memset(obj, 0, sizeof(*obj));
    free(obj);

    mutex_release(&g_utee_obj_ctx.objects_lock);
}
```

#### 2.2.2 对象验证操作（libutee 库实现）

```c
/**
 * 验证 handle 是否有效 - libutee 库实现
 * @param handle: 对象 handle（实际是 tee_obj 地址）
 * @return: 成功返回对象指针，失败返回 NULL
 */
struct trusty_tee_obj *utee_obj_get(TEE_ObjectHandle handle) {
    struct trusty_tee_obj *obj = (struct trusty_tee_obj *)handle;
    struct trusty_tee_obj *found_obj = NULL;

    if (!obj) {
        return NULL;
    }

    mutex_acquire(&g_utee_obj_ctx.objects_lock);

    /* 在 libutee 的对象链表中查找，验证 handle 有效性 */
    list_for_every_entry(&g_utee_obj_ctx.objects, found_obj,
                         struct trusty_tee_obj, link) {
        if (found_obj == obj) {
            /* handle 有效，返回对象 */
            mutex_release(&g_utee_obj_ctx.objects_lock);
            return obj;
        }
    }

    mutex_release(&g_utee_obj_ctx.objects_lock);
    return NULL;  /* handle 无效 */
}

/**
 * 设置对象忙状态 - 基于 OP-TEE 逻辑
 * @param obj: 对象指针
 * @return: 成功返回 TEE_SUCCESS，失败返回错误码
 */
TEE_Result utee_obj_set_busy(struct trusty_tee_obj *obj) {
    if (!obj) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    mutex_acquire(&obj->obj_lock);

    if (obj->busy) {
        mutex_release(&obj->obj_lock);
        return TEE_ERROR_BUSY;
    }

    obj->busy = true;
    mutex_release(&obj->obj_lock);
    return TEE_SUCCESS;
}

/**
 * 清除对象忙状态 - 基于 OP-TEE 逻辑
 * @param obj: 对象指针
 */
void utee_obj_clear_busy(struct trusty_tee_obj *obj) {
    if (!obj) {
        return;
    }

    mutex_acquire(&obj->obj_lock);
    obj->busy = false;
    mutex_release(&obj->obj_lock);
}
```

### 2.3 生命周期管理

在 Trusty 用户空间中，tee_obj 由 libutee 库管理，TA panic 时 libutee 对象链表会自动清理。

#### 2.3.1 TA panic 时的自动清理

```c
/*
 * 重要说明：在 Trusty 用户空间环境中，TA panic 时的处理与 OP-TEE 不同
 *
 * OP-TEE：
 * - tee_obj 在内核中，需要内核主动清理
 * - 内核维护 TA 上下文中的对象链表
 * - panic 时遍历链表清理所有对象
 *
 * Trusty：
 * - tee_obj 在 TA 用户空间的 libutee 库中
 * - TA panic 时，整个用户空间被销毁
 * - libutee 库中的对象链表自动清理
 *
 * 真正需要处理的是：清理该 TA 在存储 TA 中的持久化对象
 */

/**
 * TA panic 时的处理流程 - Trusty 版本
 * @param ta_uuid: panic 的 TA UUID
 */
void handle_ta_panic(const struct uuid *ta_uuid) {
    /*
     * 步骤 1: tee_obj 自动清理
     * - TA 用户空间被销毁时，libutee 库中的所有 tee_obj 自动释放
     * - 包括对象链表、内存、锁等资源
     * - 无需特殊处理
     */

    /*
     * 步骤 2: 通知存储 TA 清理该 TA 的持久化对象
     * - 这是唯一需要主动处理的部分
     */
    notify_storage_ta_cleanup(ta_uuid);
}

/**
 * libutee 库初始化 - 在 TA 启动时调用
 */
void utee_obj_context_init(void) {
    list_initialize(&g_utee_obj_ctx.objects);
    g_utee_obj_ctx.object_count = 0;
    g_utee_obj_ctx.max_objects = MAX_OBJECTS_PER_TA;
    mutex_init(&g_utee_obj_ctx.objects_lock);
}

/**
 * libutee 库清理 - 在 TA 退出时调用（可选）
 */
void utee_obj_context_cleanup(void) {
    struct trusty_tee_obj *obj, *temp;

    mutex_acquire(&g_utee_obj_ctx.objects_lock);

    /* 清理所有剩余对象 */
    list_for_every_entry_safe(&g_utee_obj_ctx.objects, obj, temp,
                              struct trusty_tee_obj, link) {
        utee_obj_free(obj);
    }

    mutex_release(&g_utee_obj_ctx.objects_lock);
    mutex_destroy(&g_utee_obj_ctx.objects_lock);
}
```

## 3. 持久化对象管理

### 3.1 存储 TA 架构

存储 TA 负责所有 tee_pobj 的统一管理。tee_pobj 在存储 TA 里，可以直接调用对应的存储接口，简化设计。

#### 3.1.1 存储 TA 数据结构（基于 OP-TEE tee_pobj）

```c
/* Trusty 持久对象 - 完全基于 OP-TEE tee_pobj 设计 */
struct trusty_pobj {
    /* 链表管理 - 对应 OP-TEE 的 TAILQ_ENTRY */
    struct list_node link;        /* 全局持久化对象链表节点 */

    /* 引用计数 - 完全保持 OP-TEE 设计 */
    uint32_t refcnt;              /* 引用计数，支持多句柄访问同一对象 */

    /* TA 隔离 - 完全保持 OP-TEE 设计 */
    struct uuid uuid;             /* 拥有该对象的 TA 的 UUID，实现 TA 隔离 */

    /* 对象标识 - 完全保持 OP-TEE 设计 */
    void *obj_id;                 /* 对象标识符，由 TA 指定 */
    uint32_t obj_id_len;          /* 对象标识符长度 */

    /* 访问控制 - 完全保持 OP-TEE 设计 */
    uint32_t flags;               /* 访问标志（读/写/共享权限） */
    uint32_t obj_info_usage;      /* 对象使用权限（密钥用途等） */

    /* 状态管理 - 完全保持 OP-TEE 设计 */
    bool temporary;               /* 临时对象标志，创建过程中为 true */
    bool creating;                /* 创建中标志，防止并发访问冲突 */

    /* 存储操作接口 - 对应 OP-TEE 的 tee_file_operations */
    const struct trusty_storage_ops *fops;

    /* Trusty 特有扩展 */
    storage_session_t session;    /* Trusty 存储会话 */
    char storage_path[256];       /* 存储路径 */
    mutex_t pobj_lock;            /* 持久对象锁 */
};

/* 存储 TA 上下文 */
struct storage_ta_context {
    /* 全局持久对象管理 */
    struct list_node pobj_list;         /* 全局 tee_pobj 链表 */
    mutex_t pobj_list_lock;              /* 全局链表锁 */
    uint32_t pobj_count;                 /* 持久对象总数 */

    /* 存储会话 */
    storage_session_t storage_session;   /* Trusty 存储会话 */

    /* 统计信息 */
    uint64_t total_operations;           /* 总操作数 */
    uint64_t failed_operations;          /* 失败操作数 */
};
```

#### 3.1.2 OP-TEE 字段映射表

| OP-TEE 字段 | Trusty TEE 字段 | 映射说明 |
|-------------|-----------------|----------|
| `TAILQ_ENTRY(tee_pobj) link` | `struct list_node link` | 全局持久对象链表节点 |
| `uint32_t refcnt` | `uint32_t refcnt` | 完全保持引用计数机制 |
| `struct tee_ta_session *ta_session` | `struct uuid uuid` | 适配为 TA UUID 标识 |
| `void *obj_id` | `void *obj_id` | 完全保持对象标识符 |
| `uint32_t obj_id_len` | `uint32_t obj_id_len` | 完全保持标识符长度 |
| `uint32_t flags` | `uint32_t flags` | 完全保持访问标志 |
| `uint32_t obj_info_usage` | `uint32_t obj_info_usage` | 完全保持使用权限 |
| `bool temporary` | `bool temporary` | 完全保持临时对象标志 |
| `bool creating` | `bool creating` | 完全保持创建中标志 |
| `const struct tee_file_operations *fops` | `const struct trusty_storage_ops *fops` | 适配 Trusty 存储操作接口 |

### 3.2 统一管控职责

存储 TA 负责所有 tee_pobj 的统一管控，基于 OP-TEE 的引用计数机制和并发控制策略。

#### 3.2.1 核心管控功能（基于 OP-TEE 逻辑）

**持久对象查找或创建：**
- 基于对象 ID 和 TA UUID 查找现有 tee_pobj
- 如果不存在则创建新的 tee_pobj
- 引用计数管理，支持多个 tee_obj 引用同一个 tee_pobj
- TA 隔离，确保不同 TA 无法访问彼此的对象

**并发控制：**
- creating 标志防止创建过程中的并发访问
- 引用计数保护，管理多句柄访问
- 全局链表锁保护 tee_pobj 链表操作

#### 3.2.2 管控操作实现（基于 OP-TEE 逻辑）

```c
/**
 * 查找或创建持久对象 - 基于 OP-TEE tee_pobj_get 逻辑
 * @param storage_ctx: 存储 TA 上下文
 * @param ta_uuid: TA UUID
 * @param obj_id: 对象标识符
 * @param obj_id_len: 对象标识符长度
 * @return: 成功返回 tee_pobj 指针，失败返回 NULL
 */
struct trusty_pobj *trusty_pobj_get(struct storage_ta_context *storage_ctx,
                                   const struct uuid *ta_uuid,
                                   const void *obj_id,
                                   uint32_t obj_id_len) {
    struct trusty_pobj *pobj;

    if (!storage_ctx || !ta_uuid || !obj_id || obj_id_len == 0) {
        return NULL;
    }

    mutex_acquire(&storage_ctx->pobj_list_lock);

    /* 查找现有对象 */
    list_for_every_entry(&storage_ctx->pobj_list, pobj, struct trusty_pobj, link) {
        if (memcmp(&pobj->uuid, ta_uuid, sizeof(*ta_uuid)) == 0 &&
            pobj->obj_id_len == obj_id_len &&
            memcmp(pobj->obj_id, obj_id, obj_id_len) == 0) {
            /* 找到对象，增加引用计数 */
            pobj->refcnt++;
            mutex_release(&storage_ctx->pobj_list_lock);
            return pobj;
        }
    }

    /* 创建新对象 */
    pobj = calloc(1, sizeof(*pobj));
    if (!pobj) {
        mutex_release(&storage_ctx->pobj_list_lock);
        return NULL;
    }

    /* 初始化持久对象 - 基于 OP-TEE 初始化逻辑 */
    list_initialize(&pobj->link);
    pobj->refcnt = 1;
    memcpy(&pobj->uuid, ta_uuid, sizeof(*ta_uuid));

    pobj->obj_id = malloc(obj_id_len);
    if (!pobj->obj_id) {
        free(pobj);
        mutex_release(&storage_ctx->pobj_list_lock);
        return NULL;
    }
    memcpy(pobj->obj_id, obj_id, obj_id_len);
    pobj->obj_id_len = obj_id_len;

    pobj->flags = 0;
    pobj->obj_info_usage = 0;
    pobj->temporary = false;
    pobj->creating = false;
    pobj->fops = &trusty_storage_ops;  /* 设置存储操作接口 */

    /* 生成存储路径 */
    generate_storage_path(pobj->storage_path, sizeof(pobj->storage_path),
                         ta_uuid, obj_id, obj_id_len);

    mutex_init(&pobj->pobj_lock);

    /* 添加到全局链表 */
    list_add_tail(&storage_ctx->pobj_list, &pobj->link);
    storage_ctx->pobj_count++;

    mutex_release(&storage_ctx->pobj_list_lock);
    return pobj;
}

/**
 * 释放持久对象引用 - 基于 OP-TEE tee_pobj_put 逻辑
 * @param storage_ctx: 存储 TA 上下文
 * @param pobj: 持久对象指针
 */
void trusty_pobj_put(struct storage_ta_context *storage_ctx, struct trusty_pobj *pobj) {
    if (!storage_ctx || !pobj) {
        return;
    }

    mutex_acquire(&storage_ctx->pobj_list_lock);

    if (--pobj->refcnt == 0) {
        /* 引用计数为 0，从列表中移除并释放 */
        list_delete(&pobj->link);
        storage_ctx->pobj_count--;
        mutex_release(&storage_ctx->pobj_list_lock);

        /* 清理资源 */
        if (pobj->obj_id) {
            free(pobj->obj_id);
        }
        mutex_destroy(&pobj->pobj_lock);
        free(pobj);
    } else {
        mutex_release(&storage_ctx->pobj_list_lock);
    }
}
```

### 3.3 接口设计

存储 TA 提供基于 OP-TEE tee_file_operations 的存储操作接口，tee_pobj 在存储 TA 里可以直接调用对应的存储接口。

#### 3.3.1 存储操作接口（基于 OP-TEE tee_file_operations）

```c
/* Trusty 存储操作接口 - 完全基于 OP-TEE tee_file_operations 设计 */
struct trusty_storage_ops {
    /* 基础文件操作 - 对应 OP-TEE 接口 */
    TEE_Result (*open)(struct trusty_pobj *pobj, size_t *size,
                      file_handle_t *fh);
    TEE_Result (*create)(struct trusty_pobj *pobj, bool overwrite,
                        const void *head, size_t head_size,
                        const void *attr, size_t attr_size,
                        file_handle_t *fh);
    TEE_Result (*close)(file_handle_t *fh);
    TEE_Result (*read)(file_handle_t fh, size_t pos,
                      void *buf, size_t *len);
    TEE_Result (*write)(file_handle_t fh, size_t pos,
                       const void *buf, size_t len);
    TEE_Result (*truncate)(file_handle_t fh, size_t len);
    TEE_Result (*remove)(struct trusty_pobj *pobj);
    TEE_Result (*rename)(struct trusty_pobj *old_pobj,
                        struct trusty_pobj *new_pobj,
                        bool overwrite);

    /* 枚举操作 - 对应 OP-TEE 的目录操作 */
    TEE_Result (*opendir)(uint32_t storage_id, void **dir_handle);
    TEE_Result (*readdir)(void *dir_handle, struct trusty_pobj **pobj);
    TEE_Result (*closedir)(void *dir_handle);

    /* 扩展操作 - Trusty 特有功能 */
    TEE_Result (*get_file_size)(file_handle_t fh, size_t *size);
    TEE_Result (*file_exists)(struct trusty_pobj *pobj, bool *exists);
};

/* 全局存储操作接口实例 */
extern const struct trusty_storage_ops trusty_storage_ops;
```

#### 3.3.2 OP-TEE 接口映射

| OP-TEE 接口 | Trusty TEE 接口 | 实现说明 |
|-------------|-----------------|----------|
| `open()` | `open()` | 使用 `storage_open_file()` 实现 |
| `create()` | `create()` | 使用 `storage_open_file()` + CREATE 标志实现 |
| `close()` | `close()` | 使用 `storage_close_file()` 实现 |
| `read()` | `read()` | 使用 `storage_read()` 实现 |
| `write()` | `write()` | 使用 `storage_write()` 实现 |
| `truncate()` | `truncate()` | 使用 `storage_set_file_size()` 实现 |
| `remove()` | `remove()` | 使用 `storage_delete_file()` 实现 |
| `rename()` | `rename()` | 使用 `storage_move_file()` 实现 |

## 4. 对象标识机制

### 4.1 标识格式

基于 OP-TEE 的对象标识机制，使用 TA 指定的对象 ID，支持任意长度和格式。

#### 4.1.1 标识格式规范（基于 OP-TEE）

**OP-TEE 兼容格式：**
- 对象 ID 由 TA 自行定义，可以是任意二进制数据
- 对象 ID 长度由 TA 指定，最大长度由实现定义
- 系统通过 TA UUID + 对象 ID 的组合确保唯一性

**推荐格式：**
```
{meaningful_name}
{uuid_string}
{binary_data}
```

**具体示例：**
```
"my_key_001"
"user_data_12345"
"550e8400-e29b-41d4-a716-************"
```

#### 4.1.2 标识符验证（基于 OP-TEE）

```c
/**
 * 验证对象 ID - 基于 OP-TEE 逻辑
 * @param obj_id: 对象标识符
 * @param obj_id_len: 对象标识符长度
 * @return: 有效返回 true，无效返回 false
 */
bool validate_object_id(const void *obj_id, uint32_t obj_id_len) {
    /* OP-TEE 允许任意对象 ID，只需要基本验证 */
    if (!obj_id || obj_id_len == 0) {
        return false;
    }

    /* 检查长度限制 */
    if (obj_id_len > TEE_OBJECT_ID_MAX_LEN) {
        return false;
    }

    /* OP-TEE 不对对象 ID 内容做特殊要求 */
    return true;
}

/**
 * 生成存储路径 - 基于 TA UUID 和对象 ID
 * @param path: 输出路径缓冲区
 * @param path_size: 路径缓冲区大小
 * @param ta_uuid: TA UUID
 * @param obj_id: 对象 ID
 * @param obj_id_len: 对象 ID 长度
 * @return: 成功返回 0，失败返回负数错误码
 */
int generate_storage_path(char *path, size_t path_size,
                         const struct uuid *ta_uuid,
                         const void *obj_id, uint32_t obj_id_len) {
    char uuid_str[37];
    char obj_id_hex[TEE_OBJECT_ID_MAX_LEN * 2 + 1];

    if (!path || !ta_uuid || !obj_id || obj_id_len == 0) {
        return -EINVAL;
    }

    /* 转换 UUID 为字符串 */
    snprintf(uuid_str, sizeof(uuid_str),
             "%08x-%04x-%04x-%04x-%012llx",
             ta_uuid->timeLow, ta_uuid->timeMid, ta_uuid->timeHiAndVersion,
             ta_uuid->clockSeqAndNode[0] << 8 | ta_uuid->clockSeqAndNode[1],
             *(uint64_t *)&ta_uuid->clockSeqAndNode[2]);

    /* 转换对象 ID 为十六进制字符串 */
    for (uint32_t i = 0; i < obj_id_len; i++) {
        snprintf(&obj_id_hex[i * 2], 3, "%02x", ((uint8_t *)obj_id)[i]);
    }

    /* 生成存储路径：ta_uuid/obj_id_hex */
    snprintf(path, path_size, "%s/%s", uuid_str, obj_id_hex);

    return 0;
}
```

### 4.2 生成规则

基于 OP-TEE 的对象标识机制，对象 ID 由 TA 自行生成和管理，系统通过 TA UUID + 对象 ID 确保唯一性。

#### 4.2.1 TA 对象 ID 管理建议

```c
/**
 * TA 对象 ID 生成建议 - 基于 OP-TEE 最佳实践
 */

/* 推荐的对象 ID 生成策略 */
enum object_id_strategy {
    OBJ_ID_STRATEGY_SEQUENTIAL,     /* 序列号策略：key_001, key_002 */
    OBJ_ID_STRATEGY_UUID,           /* UUID 策略：使用 UUID 作为对象 ID */
    OBJ_ID_STRATEGY_HASH,           /* 哈希策略：基于内容哈希生成 */
    OBJ_ID_STRATEGY_CUSTOM          /* 自定义策略：TA 自行定义 */
};

/**
 * 生成序列号对象 ID
 * @param prefix: 前缀字符串
 * @param sequence: 序列号
 * @param obj_id: 输出的对象 ID
 * @param obj_id_len: 对象 ID 缓冲区大小
 * @return: 实际对象 ID 长度
 */
uint32_t generate_sequential_object_id(const char *prefix, uint32_t sequence,
                                      void *obj_id, uint32_t obj_id_len) {
    char temp_id[64];
    uint32_t len;

    if (!prefix || !obj_id) {
        return 0;
    }

    /* 生成序列号对象 ID */
    len = snprintf(temp_id, sizeof(temp_id), "%s_%06u", prefix, sequence);

    if (len >= obj_id_len) {
        return 0;  /* 缓冲区太小 */
    }

    memcpy(obj_id, temp_id, len);
    return len;
}

/**
 * 生成 UUID 对象 ID
 * @param obj_id: 输出的对象 ID
 * @param obj_id_len: 对象 ID 缓冲区大小
 * @return: 实际对象 ID 长度
 */
uint32_t generate_uuid_object_id(void *obj_id, uint32_t obj_id_len) {
    struct uuid uuid;

    if (!obj_id || obj_id_len < sizeof(uuid)) {
        return 0;
    }

    /* 生成随机 UUID */
    if (generate_random_uuid(&uuid) != 0) {
        return 0;
    }

    memcpy(obj_id, &uuid, sizeof(uuid));
    return sizeof(uuid);
}
```

### 4.3 冲突处理

基于 OP-TEE 的设计，通过 TA UUID + 对象 ID 的组合自然避免冲突，不同 TA 可以使用相同的对象 ID 而不会冲突。

#### 4.3.1 TA 隔离机制（基于 OP-TEE）

```c
/**
 * TA 隔离验证 - 基于 OP-TEE 逻辑
 * @param pobj: 持久对象
 * @param requesting_ta_uuid: 请求访问的 TA UUID
 * @return: 有权限返回 true，无权限返回 false
 */
bool validate_ta_access(struct trusty_pobj *pobj, const struct uuid *requesting_ta_uuid) {
    if (!pobj || !requesting_ta_uuid) {
        return false;
    }

    /* OP-TEE 的 TA 隔离：只有拥有者 TA 可以访问对象 */
    return memcmp(&pobj->uuid, requesting_ta_uuid, sizeof(struct uuid)) == 0;
}

/**
 * 对象唯一性检查 - 基于 OP-TEE 逻辑
 * @param storage_ctx: 存储 TA 上下文
 * @param ta_uuid: TA UUID
 * @param obj_id: 对象 ID
 * @param obj_id_len: 对象 ID 长度
 * @return: 对象已存在返回 true，不存在返回 false
 */
bool object_exists(struct storage_ta_context *storage_ctx,
                  const struct uuid *ta_uuid,
                  const void *obj_id, uint32_t obj_id_len) {
    struct trusty_pobj *pobj;

    if (!storage_ctx || !ta_uuid || !obj_id || obj_id_len == 0) {
        return false;
    }

    mutex_acquire(&storage_ctx->pobj_list_lock);

    /* 在全局链表中查找 */
    list_for_every_entry(&storage_ctx->pobj_list, pobj, struct trusty_pobj, link) {
        if (memcmp(&pobj->uuid, ta_uuid, sizeof(*ta_uuid)) == 0 &&
            pobj->obj_id_len == obj_id_len &&
            memcmp(pobj->obj_id, obj_id, obj_id_len) == 0) {
            mutex_release(&storage_ctx->pobj_list_lock);
            return true;
        }
    }

    mutex_release(&storage_ctx->pobj_list_lock);
    return false;
}
```

## 5. 资源清理机制

### 5.1 触发条件

在 Trusty 环境中，TA panic 时的资源清理与 OP-TEE 不同：
- **tee_obj**：在 TA 用户空间，panic 时自动清理
- **tee_pobj**：在存储 TA 中，需要主动清理

#### 5.1.1 TA panic 检测（Trusty 版本）

```c
/**
 * TA panic 通知处理 - Trusty 版本
 * @param ta_uuid: 崩溃的 TA UUID
 * @return: 成功返回 0，失败返回负数错误码
 */
int handle_ta_panic_notification(const struct uuid *ta_uuid) {
    if (!ta_uuid) {
        return -EINVAL;
    }

    /*
     * 重要区别：
     * - OP-TEE：需要清理内核中的 tee_obj
     * - Trusty：tee_obj 在用户空间，自动清理
     *
     * 只需要处理：通知存储 TA 清理该 TA 的所有 tee_pobj
     */
    notify_storage_ta_cleanup(ta_uuid);

    return 0;
}
```

### 5.2 通知流程

在 Trusty 环境中，TA panic 时只需要通知存储 TA 清理该 TA 的持久对象。

#### 5.2.1 通知流程架构（Trusty 版本）

```mermaid
sequenceDiagram
    participant Kernel as Trusty 内核
    participant TA as 普通 TA (panic)
    participant StorageTA as 存储 TA

    Kernel->>Kernel: 检测到 TA panic
    Note over TA: TA 用户空间被销毁<br/>tee_obj 自动清理
    Kernel->>StorageTA: 发送清理通知<br/>(只需清理 tee_pobj)
    StorageTA->>StorageTA: 清理该 TA 的 tee_pobj
    StorageTA->>Kernel: 返回清理结果
```

#### 5.2.2 通知消息格式（简化版）

```c
/* TA 清理通知消息 - 基于 OP-TEE 简化设计 */
struct ta_cleanup_notification {
    struct uuid ta_uuid;                    /* 需要清理的 TA UUID */
    uint32_t cleanup_flags;                 /* 清理标志 */
    uint64_t timestamp;                     /* 时间戳 */
};

/* 清理标志定义 */
#define CLEANUP_FLAG_PANIC          (1 << 0)  /* TA panic 清理 */
#define CLEANUP_FLAG_NORMAL_EXIT    (1 << 1)  /* 正常退出清理 */
#define CLEANUP_FLAG_FORCE          (1 << 2)  /* 强制清理 */

/**
 * 通知存储 TA 清理 - 基于 OP-TEE 逻辑
 * @param ta_uuid: 需要清理的 TA UUID
 * @return: 成功返回 0，失败返回负数错误码
 */
int notify_storage_ta_cleanup(const struct uuid *ta_uuid) {
    struct ta_cleanup_notification notification;
    handle_t storage_ta_handle;
    ipc_msg_t msg;
    int ret;

    if (!ta_uuid) {
        return -EINVAL;
    }

    /* 准备通知消息 */
    memcpy(&notification.ta_uuid, ta_uuid, sizeof(*ta_uuid));
    notification.cleanup_flags = CLEANUP_FLAG_PANIC;
    notification.timestamp = current_time_ns();

    /* 连接存储 TA */
    ret = connect(STORAGE_TA_PORT, IPC_CONNECT_WAIT_FOR_PORT);
    if (ret < 0) {
        return ret;
    }
    storage_ta_handle = (handle_t)ret;

    /* 发送通知 */
    msg.num_iov = 1;
    msg.iov[0].iov_base = &notification;
    msg.iov[0].iov_len = sizeof(notification);
    msg.num_handles = 0;

    ret = send_msg(storage_ta_handle, &msg);
    close(storage_ta_handle);

    return ret;
}
```

### 5.3 清理策略

存储 TA 接收到清理通知后，只需要清理该 TA 的所有 tee_pobj。由于 TA 已经 panic，可以强制清理所有相关的持久对象。

#### 5.3.1 清理策略实现（Trusty 版本）

```c
/**
 * 清理 TA 的所有持久对象 - Trusty 版本
 * @param storage_ctx: 存储 TA 上下文
 * @param ta_uuid: 需要清理的 TA UUID
 * @return: 成功返回 0，失败返回负数错误码
 */
int cleanup_ta_persistent_objects(struct storage_ta_context *storage_ctx,
                                 const struct uuid *ta_uuid) {
    struct trusty_pobj *pobj, *temp;
    uint32_t cleaned_count = 0;
    uint32_t failed_count = 0;
    int ret;

    if (!storage_ctx || !ta_uuid) {
        return -EINVAL;
    }

    mutex_acquire(&storage_ctx->pobj_list_lock);

    /* 遍历全局 tee_pobj 链表，清理属于该 TA 的对象 */
    list_for_every_entry_safe(&storage_ctx->pobj_list, pobj, temp,
                              struct trusty_pobj, link) {
        if (memcmp(&pobj->uuid, ta_uuid, sizeof(*ta_uuid)) == 0) {
            /* 找到属于该 TA 的对象，进行强制清理 */
            ret = force_cleanup_pobj(storage_ctx, pobj);
            if (ret == 0) {
                cleaned_count++;
            } else {
                failed_count++;
            }
        }
    }

    mutex_release(&storage_ctx->pobj_list_lock);

    /* 更新统计信息 */
    storage_ctx->total_operations += cleaned_count + failed_count;
    if (failed_count > 0) {
        storage_ctx->failed_operations += failed_count;
    }

    return (failed_count > 0) ? -EPARTIAL : 0;
}

/**
 * 强制清理单个持久对象 - Trusty 版本
 * @param storage_ctx: 存储 TA 上下文
 * @param pobj: 要清理的持久对象
 * @return: 成功返回 0，失败返回负数错误码
 */
int force_cleanup_pobj(struct storage_ta_context *storage_ctx,
                      struct trusty_pobj *pobj) {
    int ret = 0;

    if (!storage_ctx || !pobj) {
        return -EINVAL;
    }

    mutex_acquire(&pobj->pobj_lock);

    /*
     * 重要：由于 TA 已经 panic，所有指向这个 tee_pobj 的 tee_obj
     * 都已经被自动清理了，所以可以强制清理这个 tee_pobj
     */
    pobj->refcnt = 0;  /* 强制清零引用计数 */

    /* 删除存储文件 */
    if (pobj->fops && pobj->fops->remove) {
        ret = pobj->fops->remove(pobj);
        if (ret != TEE_SUCCESS) {
            /* 即使删除文件失败，也要清理内存中的对象 */
            /* 因为 TA 已经 panic，不能留下孤儿对象 */
        }
    }

    /* 从全局链表中移除 */
    list_delete(&pobj->link);
    storage_ctx->pobj_count--;

    mutex_release(&pobj->pobj_lock);

    /* 清理对象资源 */
    if (pobj->obj_id) {
        free(pobj->obj_id);
    }
    mutex_destroy(&pobj->pobj_lock);
    free(pobj);

    return ret;
}

/**
 * 处理清理通知 - 存储 TA 的消息处理函数
 * @param notification: 清理通知消息
 * @return: 成功返回 0，失败返回负数错误码
 */
int handle_cleanup_notification(const struct ta_cleanup_notification *notification) {
    struct storage_ta_context *storage_ctx;
    int ret;

    if (!notification) {
        return -EINVAL;
    }

    /* 获取存储 TA 上下文 */
    storage_ctx = get_storage_ta_context();
    if (!storage_ctx) {
        return -ENOENT;
    }

    /* 执行清理操作 */
    ret = cleanup_ta_persistent_objects(storage_ctx, &notification->ta_uuid);

    return ret;
}
```

## 6. GP API 接口层

### 6.1 GP 标准接口实现

基于 OP-TEE 的设计，实现完整的 GP 存储 API 接口，上层调用的 API 是 GP 对应的接口。

#### 6.1.1 GP 存储 API 映射（基于 libutee 库）

```c
/**
 * TEE_AllocateTransientObject - 分配瞬时对象
 * @param objectType: 对象类型
 * @param maxObjectSize: 最大对象大小
 * @param object: 输出的对象句柄
 * @return: TEE_Result
 */
TEE_Result TEE_AllocateTransientObject(uint32_t objectType,
                                      uint32_t maxObjectSize,
                                      TEE_ObjectHandle *object) {
    struct trusty_tee_obj *obj;

    if (!object) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 通过 libutee 库分配 tee_obj */
    obj = utee_obj_alloc();
    if (!obj) {
        return TEE_ERROR_OUT_OF_MEMORY;
    }

    /* 初始化对象信息 */
    obj->info.objectType = objectType;
    obj->info.maxObjectSize = maxObjectSize;
    obj->info.objectSize = 0;
    obj->info.objectUsage = 0;
    obj->info.dataSize = 0;
    obj->info.dataPosition = 0;
    obj->info.handleFlags = 0;

    /* 返回对象地址作为 handle */
    *object = (TEE_ObjectHandle)obj;
    return TEE_SUCCESS;
}

/**
 * TEE_OpenPersistentObject - 打开持久对象
 * @param storageID: 存储 ID
 * @param objectID: 对象 ID
 * @param objectIDLen: 对象 ID 长度
 * @param flags: 访问标志
 * @param object: 输出的对象句柄
 * @return: TEE_Result
 */
TEE_Result TEE_OpenPersistentObject(uint32_t storageID,
                                   const void *objectID, uint32_t objectIDLen,
                                   uint32_t flags,
                                   TEE_ObjectHandle *object) {
    struct trusty_tee_obj *obj;
    struct uuid ta_uuid;
    TEE_Result ret;

    if (!objectID || objectIDLen == 0 || !object) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 获取当前 TA UUID */
    get_current_ta_uuid(&ta_uuid);

    /* 通过 libutee 库分配 tee_obj */
    obj = utee_obj_alloc();
    if (!obj) {
        return TEE_ERROR_OUT_OF_MEMORY;
    }

    /* 连接存储 TA */
    ret = connect(STORAGE_TA_PORT, IPC_CONNECT_WAIT_FOR_PORT);
    if (ret < 0) {
        utee_obj_free(obj);
        return TEE_ERROR_COMMUNICATION;
    }
    obj->storage_handle = (handle_t)ret;

    /* 通过 IPC 请求打开持久对象 */
    ret = utee_storage_open_object(obj->storage_handle, &ta_uuid,
                                  objectID, objectIDLen, flags, &obj->pobj);
    if (ret != TEE_SUCCESS) {
        close(obj->storage_handle);
        utee_obj_free(obj);
        return ret;
    }

    /* 设置对象信息 */
    obj->info.handleFlags = flags;

    /* 返回对象地址作为 handle */
    *object = (TEE_ObjectHandle)obj;
    return TEE_SUCCESS;
}

/**
 * TEE_CreatePersistentObject - 创建持久对象
 * @param storageID: 存储 ID
 * @param objectID: 对象 ID
 * @param objectIDLen: 对象 ID 长度
 * @param flags: 访问标志
 * @param attributes: 对象属性
 * @param initialData: 初始数据
 * @param initialDataLen: 初始数据长度
 * @param object: 输出的对象句柄
 * @return: TEE_Result
 */
TEE_Result TEE_CreatePersistentObject(uint32_t storageID,
                                     const void *objectID, uint32_t objectIDLen,
                                     uint32_t flags,
                                     TEE_ObjectHandle attributes,
                                     const void *initialData, uint32_t initialDataLen,
                                     TEE_ObjectHandle *object) {
    struct trusty_tee_obj *obj;
    struct uuid ta_uuid;
    TEE_Result ret;

    if (!objectID || objectIDLen == 0) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 获取当前 TA UUID */
    get_current_ta_uuid(&ta_uuid);

    /* 通过 libutee 库分配 tee_obj */
    obj = utee_obj_alloc();
    if (!obj) {
        return TEE_ERROR_OUT_OF_MEMORY;
    }

    /* 连接存储 TA */
    ret = connect(STORAGE_TA_PORT, IPC_CONNECT_WAIT_FOR_PORT);
    if (ret < 0) {
        utee_obj_free(obj);
        return TEE_ERROR_COMMUNICATION;
    }
    obj->storage_handle = (handle_t)ret;

    /* 通过 IPC 请求创建持久对象 */
    ret = utee_storage_create_object(obj->storage_handle, &ta_uuid,
                                    objectID, objectIDLen, flags,
                                    initialData, initialDataLen, &obj->pobj);
    if (ret != TEE_SUCCESS) {
        close(obj->storage_handle);
        utee_obj_free(obj);
        return ret;
    }

    /* 设置对象信息 */
    obj->info.dataSize = initialDataLen;
    obj->info.handleFlags = flags;

    /* 返回对象地址作为 handle */
    if (object) {
        *object = (TEE_ObjectHandle)obj;
    }

    return TEE_SUCCESS;
}

/**
 * TEE_CloseObject - 关闭对象
 * @param object: 对象句柄
 */
void TEE_CloseObject(TEE_ObjectHandle object) {
    struct trusty_tee_obj *obj;

    /* 验证 handle */
    obj = utee_obj_get(object);
    if (!obj) {
        return;
    }

    /* 关闭存储连接 */
    if (obj->storage_handle != INVALID_IPC_HANDLE) {
        close(obj->storage_handle);
    }

    /* 释放对象 */
    utee_obj_free(obj);
}

/**
 * libutee 库的存储操作接口 - 通过 IPC 与存储 TA 通信
 */

/**
 * 打开持久对象 - libutee 库实现
 * @param storage_handle: 存储 TA 连接句柄
 * @param ta_uuid: TA UUID
 * @param obj_id: 对象 ID
 * @param obj_id_len: 对象 ID 长度
 * @param flags: 访问标志
 * @param pobj: 输出的持久对象引用
 * @return: TEE_Result
 */
TEE_Result utee_storage_open_object(handle_t storage_handle,
                                   const struct uuid *ta_uuid,
                                   const void *obj_id, uint32_t obj_id_len,
                                   uint32_t flags,
                                   struct trusty_pobj **pobj) {
    struct storage_open_req req;
    struct storage_open_resp resp;
    ipc_msg_t msg;
    int ret;

    /* 准备请求消息 */
    req.cmd = STORAGE_CMD_OPEN_OBJECT;
    memcpy(&req.ta_uuid, ta_uuid, sizeof(*ta_uuid));
    req.obj_id_len = obj_id_len;
    req.flags = flags;

    /* 发送请求 */
    msg.num_iov = 2;
    msg.iov[0].iov_base = &req;
    msg.iov[0].iov_len = sizeof(req);
    msg.iov[1].iov_base = (void *)obj_id;
    msg.iov[1].iov_len = obj_id_len;
    msg.num_handles = 0;

    ret = send_msg(storage_handle, &msg);
    if (ret < 0) {
        return TEE_ERROR_COMMUNICATION;
    }

    /* 接收响应 */
    msg.num_iov = 1;
    msg.iov[0].iov_base = &resp;
    msg.iov[0].iov_len = sizeof(resp);
    msg.num_handles = 0;

    ret = read_msg(storage_handle, msg.iov[0].iov_base, msg.iov[0].iov_len);
    if (ret < 0) {
        return TEE_ERROR_COMMUNICATION;
    }

    if (resp.result != TEE_SUCCESS) {
        return resp.result;
    }

    /* 创建本地 pobj 引用 */
    *pobj = (struct trusty_pobj *)resp.pobj_handle;
    return TEE_SUCCESS;
}

/**
 * 创建持久对象 - libutee 库实现
 * @param storage_handle: 存储 TA 连接句柄
 * @param ta_uuid: TA UUID
 * @param obj_id: 对象 ID
 * @param obj_id_len: 对象 ID 长度
 * @param flags: 访问标志
 * @param initial_data: 初始数据
 * @param initial_data_len: 初始数据长度
 * @param pobj: 输出的持久对象引用
 * @return: TEE_Result
 */
TEE_Result utee_storage_create_object(handle_t storage_handle,
                                     const struct uuid *ta_uuid,
                                     const void *obj_id, uint32_t obj_id_len,
                                     uint32_t flags,
                                     const void *initial_data, uint32_t initial_data_len,
                                     struct trusty_pobj **pobj) {
    struct storage_create_req req;
    struct storage_create_resp resp;
    ipc_msg_t msg;
    int ret;

    /* 准备请求消息 */
    req.cmd = STORAGE_CMD_CREATE_OBJECT;
    memcpy(&req.ta_uuid, ta_uuid, sizeof(*ta_uuid));
    req.obj_id_len = obj_id_len;
    req.flags = flags;
    req.initial_data_len = initial_data_len;

    /* 发送请求 */
    msg.num_iov = 3;
    msg.iov[0].iov_base = &req;
    msg.iov[0].iov_len = sizeof(req);
    msg.iov[1].iov_base = (void *)obj_id;
    msg.iov[1].iov_len = obj_id_len;
    msg.iov[2].iov_base = (void *)initial_data;
    msg.iov[2].iov_len = initial_data_len;
    msg.num_handles = 0;

    ret = send_msg(storage_handle, &msg);
    if (ret < 0) {
        return TEE_ERROR_COMMUNICATION;
    }

    /* 接收响应 */
    msg.num_iov = 1;
    msg.iov[0].iov_base = &resp;
    msg.iov[0].iov_len = sizeof(resp);
    msg.num_handles = 0;

    ret = read_msg(storage_handle, msg.iov[0].iov_base, msg.iov[0].iov_len);
    if (ret < 0) {
        return TEE_ERROR_COMMUNICATION;
    }

    if (resp.result != TEE_SUCCESS) {
        return resp.result;
    }

    /* 创建本地 pobj 引用 */
    *pobj = (struct trusty_pobj *)resp.pobj_handle;
    return TEE_SUCCESS;
}

/**
 * 减少持久对象引用计数 - libutee 库实现
 * @param pobj: 持久对象引用
 */
void utee_storage_pobj_put(struct trusty_pobj *pobj) {
    /* 在实际实现中，这里应该通过 IPC 通知存储 TA 减少引用计数 */
    /* 为了简化，这里只是一个占位符 */
    (void)pobj;
}
```

## 7. 实现注意事项

### 7.1 关键技术点

在实现该设计方案时，需要特别注意以下关键技术点，确保系统的正确性和可靠性。

#### 7.1.1 OP-TEE vs Trusty 的关键差异

**OP-TEE 模型：**
- tee_obj 和 tee_pobj 都在内核中
- 内核维护 TA 上下文中的对象链表
- TA panic 时需要内核主动清理所有对象

**Trusty 模型：**
- tee_obj 在 TA 用户空间的 libutee 库中
- tee_pobj 在存储 TA 中，统一管理
- TA panic 时，libutee 库中的 tee_obj 自动清理，只需清理存储 TA 中的 tee_pobj

**设计适配要点：**
- 保持 OP-TEE 的对象语义和 API 兼容性
- 使用 libutee 库管理 tee_obj，保持链表结构
- 通过 IPC 与存储 TA 通信，管理 tee_pobj
- 专注于持久对象的清理机制

#### 7.1.2 并发控制

```c
/* 并发控制最佳实践 - 基于 libutee 库 */

/* 1. libutee 库锁的获取顺序 - 避免死锁 */
void acquire_utee_locks_safely(struct trusty_tee_obj *obj1,
                               struct trusty_tee_obj *obj2) {
    /* 总是先获取全局锁，再获取对象锁 */
    mutex_acquire(&g_utee_obj_ctx.objects_lock);

    /* 按地址顺序获取对象锁，避免死锁 */
    if (obj1 < obj2) {
        mutex_acquire(&obj1->obj_lock);
        if (obj2) {
            mutex_acquire(&obj2->obj_lock);
        }
    } else {
        if (obj2) {
            mutex_acquire(&obj2->obj_lock);
        }
        mutex_acquire(&obj1->obj_lock);
    }
}

/* 2. 引用计数的原子操作 - 通过 IPC 实现 */
bool atomic_increment_pobj_refcnt(struct trusty_pobj *pobj) {
    /* 在 Trusty 中，引用计数操作通过 IPC 发送给存储 TA */
    /* 存储 TA 负责原子性地处理引用计数 */
    return utee_storage_pobj_get(pobj);
}

/* 3. busy 标志的使用 - 基于 OP-TEE 逻辑 */
TEE_Result set_object_busy_safe(struct trusty_tee_obj *obj) {
    return utee_obj_set_busy(obj);
}
```

#### 7.1.3 内存管理

```c
/* 内存管理最佳实践 - 基于 libutee 库 */

/* 1. 安全的对象分配 - 使用 libutee 库 */
struct trusty_tee_obj *safe_obj_alloc(void) {
    return utee_obj_alloc();
}

/* 2. 安全的对象释放 - 使用 libutee 库 */
void safe_obj_free(struct trusty_tee_obj *obj) {
    utee_obj_free(obj);
}

/* 3. libutee 库的内存管理 */
void utee_memory_management_best_practices(void) {
    /*
     * libutee 库内存管理要点：
     *
     * 1. 对象分配时检查资源限制
     * 2. 对象释放时安全清除敏感数据
     * 3. 使用链表统一管理所有对象
     * 4. TA 退出时自动清理所有资源
     * 5. 通过 IPC 与存储 TA 通信，避免直接内存访问
     */
}

/* 4. 安全内存清除 - 防止信息泄露 */
void secure_memset(void *ptr, int value, size_t size) {
    volatile uint8_t *p = (volatile uint8_t *)ptr;
    while (size--) {
        *p++ = value;
    }
    /* 确保编译器不会优化掉这个操作 */
    __asm__ __volatile__("" : : "r"(ptr) : "memory");
}

/* 5. IPC 通信的内存管理 */
int safe_ipc_communication(handle_t handle, const void *req, size_t req_size,
                          void *resp, size_t resp_size) {
    ipc_msg_t msg;
    int ret;

    /* 准备发送消息 */
    msg.num_iov = 1;
    msg.iov[0].iov_base = (void *)req;
    msg.iov[0].iov_len = req_size;
    msg.num_handles = 0;

    ret = send_msg(handle, &msg);
    if (ret < 0) {
        return ret;
    }

    /* 接收响应 */
    ret = read_msg(handle, resp, resp_size);
    if (ret < 0) {
        /* 清除可能的敏感数据 */
        secure_memset(resp, 0, resp_size);
        return ret;
    }

    return 0;
}
```

### 7.2 兼容性考虑

#### 7.2.1 与现有系统的兼容性

**Trusty 存储服务兼容性：**
- 确保新的对象管理机制不影响现有的文件操作接口
- 保持与现有存储端口的兼容性
- 维护现有的事务处理和错误处理机制

**GP API 兼容性：**
- 完全实现 GP 标准的存储 API 接口
- 保持与 OP-TEE 的 API 兼容性
- 确保错误码和返回值的一致性

#### 7.2.2 性能影响评估

**内存使用影响：**
- 新增的对象管理结构预计增加内存使用约 1-3MB
- 通过引用计数机制减少内存占用
- 实施内存使用监控，确保不超过系统限制

**存储性能影响：**
- 基于 OP-TEE 成熟设计，性能影响最小
- 通过存储 TA 统一管理，提高缓存效率
- 实施性能基准测试，确保满足性能要求

### 7.3 设计限制说明

#### 7.3.1 多实例支持限制

**当前限制：**
- 本设计方案主要针对单实例 TA 环境
- 多实例 TA 的对象隔离和资源管理需要额外的设计考虑

**后续版本规划：**
- 多实例支持将在 v2.0 版本中单独设计和实现
- 需要扩展对象标识机制，支持实例级别的隔离

#### 7.3.2 Panic 流程限制

**当前限制：**
- 本文档暂不涉及 TA panic 时的特殊处理流程
- Panic 状态下的资源清理策略需要特殊考虑

**后续版本规划：**
- Panic 流程将在 v2.0 版本中单独设计
- 需要设计 panic 状态下的快速资源清理策略

---

**文档版本：** v1.0 - 基于 OP-TEE 设计
**最后更新：** 2024年12月
**状态：** 设计完成，待实现

**核心设计原则：**
1. **完全基于 OP-TEE 架构**：tee_obj + tee_pobj 双层对象模型
2. **libutee 库实现**：tee_obj 在 libutee 库中管理，保持链表结构
3. **IPC 通信机制**：通过 IPC 与存储 TA 通信，管理 tee_pobj
4. **GP 标准兼容**：上层提供完整的 GP 存储 API 接口
5. **简化清理机制**：TA panic 时只需清理存储 TA 中的 tee_pobj

**实现要点：**
- tee_obj 由 libutee 库管理，使用链表数据结构
- tee_pobj 由存储 TA 统一管理，基于引用计数机制
- GP API 通过 libutee 库实现，与存储 TA 进行 IPC 通信
- TA panic 时，libutee 库中的对象自动清理，存储 TA 收到通知后清理 tee_pobj

**注意：** 本文档描述的是基于 OP-TEE 设计的可信存储方案，适配 Trusty 用户空间环境。多实例支持和 panic 流程将在后续版本中单独设计和实现。在实施过程中，应该优先实现核心功能，然后逐步扩展高级特性。
```mermaid
graph LR
    subgraph "TA 对象管理器"
        A[ta_object_manager]
        A --> B[transient_objects 链表头]
    end
    
    subgraph "瞬时对象链表"
        B --> C[对象句柄 1]
        C --> D[对象句柄 2]
        D --> E[对象句柄 N]
        E --> B
    end
    
    subgraph "对象句柄详情"
        C --> F[data_buffer]
        C --> G[persistent_ref]
        D --> H[data_buffer]
        D --> I[persistent_ref]
    end
```
